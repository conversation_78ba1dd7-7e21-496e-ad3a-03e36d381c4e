<template>
  <div>
    <div class="card card-flush">
      <div class="card-body pt-0">
        <div class="table-responsive">
          <table
            id="kt_project_users_table"
            class="table table-row-bordered table-row-dashed gy-4 align-middle fw-bold"
          >
            <thead class="fs-7 text-gray-400 text-uppercase">
              <tr>
                <th class="min-w-150px">Company Name</th>
                <th class="min-w-90px">Job Title</th>
                <th class="min-w-90px">Job Type</th>
                <th class="min-w-90px">Location</th>
                <th class="min-w-90px">Price</th>
                <th class="min-w-50px">Actions</th>
              </tr>
            </thead>
            <tbody class="fs-6">
              <tr v-for="job in jobs" :key="job.id">
                <td>
                  <span>{{ job.company.name || job.company }}</span>
                </td>
                <td>
                  <span>{{ job.title }}</span>
                </td>
                <td>
                  <span>{{ job.employment_type }}</span>
                </td>
                <td>
                  <span>{{ job.location }}</span>
                </td>
                <td>
                  <span>{{ job.pay_amount }}</span>
                </td>
                <td>
                  <div class="dropdown">
                    <button
                      class="btn btn-sm btn-light px-5 pt-3 pb-2 rounded-0 dropdown-toggle"
                      type="button"
                      id="dropdownMenuButton"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                    >
                      Actions
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                      <li><a class="dropdown-item" :href="job.url || '#'">View jobs</a></li>
                      <li><a class="dropdown-item" :href="job.apply_url || '#'">Apply</a></li>
                    </ul>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';

export default {
  name: 'ListView',
  props: {
    jobs: {
      type: Array,
      required: true,
    },
    noSearchResult: {
      type: Boolean,
      required: true,
    },
    noJobsLeft: {
      type: Boolean,
      required: true,
    },
    loadMoreJobs: {
      type: Function,
      required: false,
    },
  },
  setup(props) {


    return {
    };
  },
};
</script>

<style scoped>
.table {
  width: 100%;
  margin-bottom: 1rem;
}
.btn-light {
  transition: all 0.3s ease;
}
.btn-light:hover {
  opacity: 0.8;
}
</style>