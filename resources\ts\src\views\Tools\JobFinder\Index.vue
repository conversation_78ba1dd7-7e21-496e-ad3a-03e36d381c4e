<template>
  <div>
    <div class="full-view-banner banner" :style="{ backgroundImage: `url(${banner.imagefullpath})` }">
      <div v-if="banner.video" class="banner-video" v-html="banner.video"></div>
      <div class="banner_detail_box w-450px">
        <h1 class="fw-normal display-4 mb-4 fs-4x text-dark">Job Finder</h1>
        <p class="fw-normal text-dark mb-10" style="font-size: 14px;">Search and save Jobs by industry and location.</p>
        <div class="row mt-5">
          <div class="col-8 col-sm-6 col-md-12">
            <button type="button" class="btn btn-black-custom btn-lg rounded-0 w-100 p-md-5"
              @click="scrollToSection('FilteredSection')"> Search Jobs </button>
          </div>
        </div>
        <div class="row mt-5">
          <div class="col-8 col-sm-6 col-md-12">
            <button type="button" class="btn btn-white-custom btn-lg rounded-0 w-100 p-md-5"
              @click="openSavedJobsModal"> Your Saved Jobs </button>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-12">
      <div v-if="currentTab === 'grid' || currentTab === 'list'">
        <div class="row d-flex bg-white p-10 bottom-1 align-items-center">
          <div class="col-xl-3 mb-5 position-relative">
            <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                  transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                <path
                  d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                  fill="currentColor"></path>
              </svg>
            </span>
            <Field class="form-control form-control-solid ps-10" type="text"
              placeholder="Search job title or keyword" name="what" autocomplete="off" v-model="filters.what" />
          </div>
          <div class="col-xl-3 mb-5 position-relative">
            <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                  transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                <path
                  d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                  fill="currentColor"></path>
              </svg>
            </span>
            <Field class="form-control form-control-solid ps-10" type="text"
              placeholder="Search city, state, territory, postcode" name="where" autocomplete="off"
              v-model="filters.where" />
          </div>
          <div class="col-xl-3 mb-5">
            <Field v-slot="{ field }" name="EmploymentType">
              <Multiselect
                class="form-control form-control-solid py-2 fs-6"
                v-model="filters.jobtype"
                v-bind="field"
                :searchable="false"
                placeholder="Employment Type"
                :options="employmentTypes"
                :multiple="true"
                :resolve-on-load="false"
              />
            </Field>
          </div>
          <div class="col-xl-2 mb-5">
            <Field v-slot="{ field }" name="CourseType">
              <Multiselect class="form-control form-control-solid py-2 fs-6" v-model="filters.courseType"
                v-bind="field" :searchable="false" placeholder="Seniority Level" :options="industries"
                :resolve-on-load="false" />
            </Field>
          </div>
          <div class="col-xl-1 mb-5">
            <button type="button"
              class="btn btn-light d-flex justify-content-center align-items-center py-auto w-100"
              data-toggle="button" aria-pressed="false" autocomplete="off" @click="applyFilter">
              Find Jobs
            </button>
          </div>
        </div>
      </div>
      <div v-else-if="currentTab === 'map'">
        <div class="row d-flex justify-content-center bg-white p-10 bottom-1 align-items-center">
          <div class="col-3 position-relative">
            <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                  transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                <path
                  d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                  fill="currentColor"></path>
              </svg>
            </span>
            <Field class="form-control form-control-solid ps-10" type="text"
              placeholder="Search job title or keyword" name="what" autocomplete="off" v-model="filters.what" />
          </div>
          <div class="col-3 position-relative">
            <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                  transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                <path
                  d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                  fill="currentColor"></path>
              </svg>
            </span>
            <Field class="form-control form-control-solid ps-10" type="text"
              placeholder="Search city, state, territory, postcode" name="where" autocomplete="off"
              v-model="filters.where" />
          </div>
          <div class="col-1">
            <button type="button" class="btn btn-light d-flex justify-content-center align-items-center py-auto w-100"
              @click="applyFilter">
              Find Jobs
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="row p-5 custom-row">
      <div class="col-12 col-md-6">
        <div class="row d-flex align-items-center">
          <div class="col-12 col-md-3 d-flex justify-content-start">
            <h3 class="fw-bold my-0 text-center">
              <span>{{ totalRecord }} Jobs Found</span>
            </h3>
          </div>
          <div class="col-12 col-md-4 mb-5">
            <Field v-slot="{ field }" name="CourseType">
              <Multiselect class="bg-white form-control form-control-solid py-3 fs-6"
                v-model="filters.courseType" v-bind="field" :searchable="false" placeholder="Recently Added"
                :options="industries" :resolve-on-load="false" />
            </Field>
          </div>
        </div>
      </div>
      <div class="col-12 col-md-6 d-flex justify-content-end align-items-center text-center custom-nav-container">
        <p class="mb-0 me-5">VIEW JOBS IN BLOCKS, ROWS OR MAP VIEW</p>
        <ul class="nav nav-pills me-6 mb-2 mb-sm-0">
          <li class="nav-item m-0">
            <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary me-3"
              :class="{ active: currentTab === 'grid' }" data-bs-toggle="tab" @click="currentTab = 'grid'"
              href="#kt_project_users_card_pane">
              <span class="svg-icon svg-icon-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24">
                  <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <rect x="5" y="5" width="5" height="5" rx="1" fill="currentColor" />
                    <rect x="14" y="5" width="5" height="5" rx="1" fill="currentColor" opacity="0.3" />
                    <rect x="5" y="14" width="5" height="5" rx="1" fill="currentColor" opacity="0.3" />
                    <rect x="14" y="14" width="5" height="5" rx="1" fill="currentColor" opacity="0.3" />
                  </g>
                </svg>
              </span>
            </a>
          </li>
          <li class="nav-item m-0">
            <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary me-3"
              :class="{ active: currentTab === 'list' }" data-bs-toggle="tab" @click="currentTab = 'list'"
              href="#kt_project_users_table_pane">
              <svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="38" height="38" rx="5" fill="white" />
                <g clip-path="url(#clip0_207_1107)">
                  <path
                    d="M14 11H24C24.5304 11 25.0391 11.2107 25.4142 11.5858C25.7893 11.9609 26 12.4696 26 13V16C26 16.5304 25.7893 17.0391 25.4142 17.4142C25.0391 17.7893 24.5304 18 24 18H14C13.4696 18 12.9609 17.7893 12.5858 17.4142C12.2107 17.0391 12 16.5304 12 16V13C12 12.4696 12.2107 11.9609 12.5858 11.5858C12.9609 11.2107 13.4696 11 14 11ZM14 12C13.7348 12 13.4804 12.1054 13.2929 12.2929C13.1054 12.4804 13 12.7348 13 13V16C13 16.2652 13.1054 16.5196 13.2929 16.7071C13.4804 16.8946 13.7348 17 14 17H24C24.2652 17 24.5196 16.8946 24.7071 16.7071C24.8946 16.5196 25 16.2652 25 16V13C25 12.7348 24.8946 12.4804 24.7071 12.2929C24.5196 12.1054 24.2652 12 24 12H14ZM14 20H24C24.5304 20 25.0391 20.2107 25.4142 20.5858C25.7893 20.9609 26 21.4696 26 22V25C26 25.5304 25.7893 26.0391 25.4142 26.4142C25.0391 26.7893 24.5304 27 24 27H14C13.4696 27 12.9609 26.7893 12.5858 26.4142C12.2107 26.0391 12 25.5304 12 25V22C12 21.4696 12.2107 20.9609 12.5858 20.5858C12.9609 20.2107 13.4696 20 14 20ZM14 21C13.7348 21 13.4804 21.1054 13.2929 21.2929C13.1054 21.4804 13 21.7348 13 22V25C13 25.2652 13.1054 25.5196 13.2929 25.7071C13.4804 25.8946 13.7348 26 14 26H24C24.2652 26 24.5196 25.8946 24.7071 25.7071C24.8946 25.5196 25 25.2652 25 25V22C25 21.7348 24.8946 21.4804 24.7071 21.2929C24.5196 21.1054 24.2652 21 24 21H14Z"
                    fill="#747474" />
                </g>
                <defs>
                  <clipPath id="clip0_207_1107">
                    <rect width="16" height="16" fill="white" transform="translate(11 11)" />
                  </clipPath>
                </defs>
              </svg>
            </a>
          </li>
          <li class="nav-item m-0">
            <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary me-3"
              :class="{ active: currentTab === 'map' }" data-bs-toggle="tab" @click="currentTab = 'map'"
              href="#kt_project_users_map_pane">
              <svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="38" height="38" rx="5" fill="#8E8E8E" />
                <path
                  d="M19 27C19 27 25 21.314 25 17C25 15.4087 24.3679 13.8826 23.2426 12.7574C22.1174 11.6321 20.5913 11 19 11C17.4087 11 15.8826 11.6321 14.7574 12.7574C13.6321 13.8826 13 15.4087 13 17C13 21.314 19 27 19 27ZM19 20C18.2044 20 17.4413 19.6839 16.8787 19.1213C16.3161 18.5587 16 17.7956 16 17C16 16.2044 16.3161 15.4413 16.8787 14.8787C17.4413 14.3161 18.2044 14 19 14C19.7956 14 20.5587 14.3161 21.1213 14.8787C21.6839 15.4413 22 16.2044 22 17C22 17.7956 21.6839 18.5587 21.1213 19.1213C20.5587 19.6839 19.7956 20 19 20Z"
                  fill="white" />
              </svg>
            </a>
          </li>
        </ul>
      </div>
    </div>

    <div class="tab-content">
      <div class="tab-pane fade" @click="setupTabEvents" :class="{ 'show active grid': currentTab === 'grid' }"
        id="kt_project_users_card_pane">
        <Jobs :jobs="jobs" :no-search-result="noSearchResult" :no-jobs-left="noJobsLeft" @search="handleSearch"
          @page-change="handlePageChange" />
      </div>
      <div class="tab-pane fade" @click="setupTabEvents" :class="{ 'show active list': currentTab === 'list' }"
        id="kt_project_users_table_pane">
        <ListView :jobs="jobs" :no-search-result="noSearchResult" :no-jobs-left="noJobsLeft" @search="handleSearch"
          @page-change="handlePageChange" />
      </div>
      <div class="tab-pane fade" :class="{ 'show active map': currentTab === 'map' }"
        id="kt_project_users_map_pane">
        <MapView :jobs="jobs" :no-search-result="noSearchResult" :no-jobs-left="noJobsLeft" @search="handleSearch"
          @page-change="handlePageChange"  />
      </div>
    </div>

    <div v-if="noSearchResult" class="text-center mt-4">
      <p>No search results found.</p>
    </div>

    <div class="d-flex justify-content-end py-10" v-if="currentTab !== 'map' && totalRecord > 0">
      <TablePagination
        :current-page="page"
        :total-pages="lastPage"
        :total-items="totalRecord"
        :items-per-page="itemsPerPage"
        @page-change="handlePageChange"
      />
    </div>

    <SelectedJobFinder />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import TablePagination from '@/components/kt-datatable/table-partials/table-content/table-footer/TablePagination.vue';
import { Field } from 'vee-validate';
import { useRoute } from 'vue-router';
import Multiselect from '@vueform/multiselect';
import BlockView from '@/components/Jobs/BlockView.vue';
import ListView from '@/components/Jobs/ListView.vue';
import MapView from '@/components/Jobs/MapView.vue';
import Jobs from '@/components/Jobsreusecomponent/Jobs.vue';
import SelectedJobFinder from '@/components/Jobs/SelectedJobFinder.vue';
import { Modal } from 'bootstrap';
import axios from 'axios';

export default defineComponent({
  components: {
    Field,
    Multiselect,
    BlockView,
    SelectedJobFinder,
    ListView,
    MapView,
    TablePagination,
    Jobs,
  },
  props: ['template'],
  setup(props) {
    const currentTab = ref('grid');
    const jobs = ref<any[]>([]);
    const filters = ref({
      what: null,
      where: null,
      sort: 'date',
      distance: null,
      jobtype: null,
      courseType: null,
    });
    const employmentTypes = ['part-time', 'full-time', 'internship', 'casual', 'contract', 'temporary'];
    const industries = ref<string[]>([]);
    const totalRecord = ref(0);
    const lastPage = ref(0);
    const noJobsLeft = ref(false);
    const noSearchResult = ref(false);
    const selectedJobs = ref([]);
    const route = useRoute();
    const favJobs = ref([]);
    const banner = ref({
      trailer_video: null,
      video: null,
      imagefullpath: null,
    });
    const page = ref(1);
    const itemsPerPage = ref(6);

    const debounce = (func: Function, wait: number) => {
      let timeout: NodeJS.Timeout | null = null;
      return (...args: any[]) => {
        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
      };
    };

    const fetchJobs = async (pageNum: number = 1) => {
      try {
        const url = `/api/companies/${route.params.id}/jobs_opportunities`;
        const response = await axios.get(url, {
          params: {
            title: filters.value.what?.trim() || '',
            location: filters.value.where || null,
            employment_type: filters.value.jobtype || null,
            industry: filters.value.courseType || null,
            page: pageNum,
            per_page: itemsPerPage.value,
          },
        });

        jobs.value = response.data.data.map(job => ({
          id: job.id,
          posted: job.created_at,
          company: job.company || 'Unknown Company',
          title: job.title,
          location: job.location || 'N/A',
          type: job.employment_type || 'N/A',
          pay_frequency: job.pay_frequency || 'N/A',
          pay_amount: job.pay_amount || 'N/A',
          employment_type: job.employment_type || 'N/A',
          industry: job.industry || 'N/A',
        }));

        noSearchResult.value = response.data.data.length === 0 && (filters.value.what || filters.value.where || filters.value.jobtype || filters.value.courseType);
        totalRecord.value = response.data.meta.total;
        lastPage.value = response.data.meta.last_page;
        page.value = pageNum;
        noJobsLeft.value = response.data.data.length === 0 && pageNum >= response.data.meta.last_page && !filters.value.what && !filters.value.where && !filters.value.jobtype && !filters.value.courseType;
      } catch (error) {
        console.error('Error fetching jobs:', error);
        noSearchResult.value = !!(filters.value.what || filters.value.where || filters.value.jobtype || filters.value.courseType);
        jobs.value = [];
        totalRecord.value = 0;
        lastPage.value = 1;
        noJobsLeft.value = !filters.value.what && !filters.value.where && !filters.value.jobtype && !filters.value.courseType;
      }
    };

    

    const handleJobFilterChange = async () => {
      page.value = 1; 
      jobs.value = [];
      await fetchJobs(page.value);
    };

    const handlePageChange = async (newPage: number) => {
      page.value = newPage;
      jobs.value = [];
      await fetchJobs(newPage);
      scrollToSection('FilteredSection');
    };

    const handleSearch = (searchParams: { searchQuery: string; filters: { location: string; courseType: string } }) => {
      filters.value.what = searchParams.searchQuery;
      filters.value.where = searchParams.filters.location;
      filters.value.courseType = searchParams.filters.courseType;
      handleJobFilterChange();
    };

    const setupTabEvents = () => {
      const gridTab = document.querySelector('a[href="#kt_project_users_card_pane"]');
      const listTab = document.querySelector('a[href="#kt_project_users_table_pane"]');
      const mapTab = document.querySelector('a[href="#kt_project_users_map_pane"]');
       

      gridTab?.addEventListener('shown.bs.tab', () => {
        currentTab.value = 'grid';
      });

      listTab?.addEventListener('shown.bs.tab', () => {
        currentTab.value = 'list';
      });

      mapTab?.addEventListener('shown.bs.tab', () => {
        currentTab.value = 'map';
      });
    };

    const scrollToSection = (sectionId: string) => {
      const section = document.getElementById(sectionId);
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' });
      }
    };

    const openSavedJobsModal = () => {
      scrollToSection('FilteredSection');
      const modalElement = document.getElementById('kt_modal_SelectedJobFinder');
      if (modalElement) {
        const modal = new Modal(modalElement);
        modal.show();
      } else {
        console.error('Modal element #kt_modal_SelectedJobFinder not found in DOM');
      }
    };

    const debouncedFilterChange = debounce(() => {
      handleJobFilterChange();
    }, 300);

    const applyFilter = () => {
      handleJobFilterChange();
    };

    onMounted(() => {
      setupTabEvents();
      fetchJobs();
    });
    

    return {
      banner,
      openSavedJobsModal,
      handleJobFilterChange,
      filters,
      jobs,
      totalRecord,
      lastPage,
      scrollToSection,
      page,
      setupTabEvents,
      noSearchResult,
      noJobsLeft,
      currentTab,
      applyFilter,
      itemsPerPage,
      handlePageChange,
      handleSearch,
      employmentTypes,
      industries,
    };
  },
});
</script>

<style scoped>
    #kt_modal_course {
        z-index: 9999;
    }

    #kt_modal_course .modal-dialog {
        padding: 2.25rem;
    }

    /* Add this CSS to your stylesheet */
    .noUi-tooltip {
        color: white;
        /* Text color */
        background: black;
        /* Background color */
        border: none;
        /* Remove border */
    }

    .nav-item .active {
        background-color: #8E8E8E !important;
    }


    /* courseFinder Added */

    .app-container {
        background-color: #fff;
    }

    .wrap {
        overflow: hidden;
        max-width: 75ch;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .btn-white-custom {
        background: #fff;
        color: #000;
    }

    .btn-border-custom {
        border: 1px solid white;
        color: white;
        background: gray;
    }

    .btn-black-custom:hover,
    .btn-white-custom {
        background-color: #fff !important;
        color: #000 !important;
    }

    .btn-black-custom,
    .btn-white-custom:hover {
        background-color: #000 !important;
        color: #fff !important;
    }

    .btn-white-custom:hover,
    .btn.btn-white-custom:hover:not(.btn-active) {
        background-color: #000 !important;
        color: #fff !important;
    }

    .module-sections {
        overflow: auto hidden;
        margin-left: -30px;
        margin-right: -30px;
        position: relative;
        z-index: 100;
    }

    .sticky-top {
        position: fixed;
        min-width: calc(100% - 140px);
    }

    .module-section {
        border-top: 1px solid;
        border-bottom: 1px solid;
        border-left: 1px solid;
        cursor: pointer;
        height: 100px;
    }

    .module-sections>.text-center:last-of-type>.module-section {
        border-right: 1px solid;
    }

    .app-content {
        padding: 0px;
    }

    /* .banner_detail_box {
            position: absolute;
            top: 50%;
            left: 10%;
        } */

    .banner_detail_box {
        position: absolute;
        top: 50%;
        left: 20%;
        transform: translate(-50%, -50%);
    }

    .banner_tbc_box {
        position: absolute;
        top: 30%;
        padding: 0 10%;
        width: 100%;
    }

    .modal-backdrop {
        opacity: 0.8 !important;
    }

    .banner {
        min-height: 803px !important;

    }


    /* .sticky-top+.section-content {
                                                        margin-top: 150px;
                                                    } */

    .section-content {
        margin-top: 50px;
        padding-bottom: 50px;
    }

    .section-content iframe {
        width: 100% !important;
    }

    .section-content iframe.wistia_embed {
        height: 100% !important;
    }

    .section-content img {
        max-width: 100%;
    }

    .section-content p img,
    .section-content p iframe {
        margin-bottom: -1rem;
    }

    .pointer {
        cursor: pointer;
    }

    .overlay {
        overflow: overlay;
    }

    .related {
        right: 5% !important;
    }

    .banner {
        background-color: #bbb;
        display: block;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;
        overflow: hidden;
        min-height: calc(56.25vw - 149px);
    }

    .full-view-banner {
        margin-left: -30px;
        margin-right: -30px;
    }

    .banner-video {
        height: 100%;
    }

    .banner-video>video {
        /* height: 100%; */
        width: 101% !important;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .froala-response,
    .teacher-feedback {
        height: 300px;
        overflow: auto;
        padding: 20px;
        border-radius: 10px;
    }

    .froala-response {
        background-color: #fff;
        border: 1px solid #bbb;
    }

    .froala-response iframe {
        width: 100%;
    }

    .froala-response img {
        max-width: 100%;
    }

    div#kt_app_content {
        padding-top: 0px;
        padding-bottom: 0px;
    }

    @media (max-width: 1280px) {
        .banner {
            /* height: calc(56.25vw - 140px); */
            height: 56.25vw;
        }

        .banner_detail_box {
            left: 40%;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(65vw + 65vh) !important;
        }
    }

    @media (min-width: 992px) {

        .sticky-top+.section-content {
            margin-top: 100px;
        }

        .module-sections {
            animation-name: backtooriginal;
            animation-duration: 0.2s;
            animation-fill-mode: forwards;
        }

        .sticky-top {
            animation-name: stick-top;
            animation-duration: 0.2s;
            animation-fill-mode: forwards;
        }

        @keyframes stick-top {
            from {
                top: 5px;
            }

            100% {
                top: 0px;
            }
        }

        @keyframes backtooriginal {
            from {
                top: -5px;
            }

            100% {
                top: 0px;
            }
        }
    }

    @media (max-width: 991px) {

        .full-view-banner,
        .module-sections {
            margin-left: -20px;
            margin-right: -20px;
        }

        .full-view-banner {
            margin-top: 58.16px;
        }

        .sticky-top {
            top: 119px;
            min-width: 100%;
        }

        .module-section {
            height: 100px;
        }
    }



    @media (max-width: 991px) and (min-width: 768px) and (orientation:portrait) {
        .banner {
            height: 86.25vw;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(66vw + 66vh) !important;
        }
    }

    @media (max-width: 991px) and (orientation:landscape) {
        .banner-video>video {
            height: auto !important;
            width: calc(70vw + 70vh) !important;
        }
    }

    @media (max-width: 767px) {

        .banner {
            height: calc(100vh - 300px);
        }

        .banner_detail_box {
            left: 50%;
        }

        .sticky-top {
            margin-top: 10px;
        }
    }

    @media (max-width: 575px) {
        div#kt_app_content {
            padding-top: 30px;
        }

        .full-view-banner {
            margin-top: 0;
        }

        .banner_detail_box {
            width: 70vw !important;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(90vw + 90vh) !important;
        }

    }
</style>