<template>
    <div class="full-view-banner banner" v-bind:style="{ 'backgroundImage': 'url(' + banner.imagefullpath + ')' }">
        <div v-if="banner.video" class="banner-video" v-html="banner.video"></div>
        <!-- <div style="position:absolute;width:100%;height:100%;opacity:.3;background:#DFDFDF;"></div> -->
        <div class="position-absolute bottom-0 ps-18 mb-18 start-0 text-black w-100">
            <h1 class="fw-normal display-4 mb-2 fs-4x">Employer Search</h1>
            <h3 class="fw-normal" style="font-size: 18px;">Search and explore companies by industry and location.</h3>
            <div class=" w-450px">
                <div class="row">
                    <div class="col-8 col-sm-6 col-md-12">
                        <button type="button" class="btn btn-black-custom btn-lg rounded-0 w-100 p-md-5 mb-5"
                            @click="scrollToSection('FilteredSection')">Search For Employers</button>

                        <button class="btn btn-border-custom btn-lg rounded-0 w-100 p-md-5"
                            @click="getSelectedScholarships()" data-bs-toggle="modal"
                            data-bs-target="#kt_modal_SelectedScholarships" style="font-size: 14px !important;">Your
                            Saved Employers</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="full-view-banner row bg-black black-strip" id="FilteredSection">
    </div>
    <div class="mt-12">
        <div class="p-2 card">
            <div class="card-body p-3 row justify-content-center align-items-center">
                <div class="col-12 col-md-3 mt-2 position-relative">
                    <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                            <path
                                d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                fill="currentColor"></path>
                        </svg>
                    </span>
                    <input v-model="searchQuery" class="form-control form-control-solid ps-10" type="text"
                        placeholder="Search company name or keyword" autocomplete="off" />
                </div>
                <div class="col-12 col-md-3 mt-2 position-relative">
                    <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" id="IconChangeColor" height="16"
                            width="16">
                            <path
                                d="M168.3 499.2C116.1 435 0 279.4 0 192C0 85.96 85.96 0 192 0C298 0 384 85.96 384 192C384 279.4 ***********.7 499.2C203.4 514.5 180.6 514.5 168.3 499.2H168.3zM192 256C227.3 ***********.3 256 192C256 156.7 227.3 128 192 128C156.7 ***********.7 128 192C128 227.3 156.7 256 192 256z"
                                id="mainIconPathAttribute" fill="#737373" stroke-width="0.2" stroke="#ff0000">
                            </path>
                        </svg>
                    </span>
                    <!-- Search city, state, territory, postcode -->
                    <input v-model="locationQuery" class="form-control form-control-solid ps-10 " type="text"
                        placeholder="Please enter your state" autocomplete="off" />
                </div>
                <div class="col-12 col-md-3 mt-2">
                    <Field v-slot="{ field }" name="industryCategory">
                        <Multiselect class="form-control form-control-solid py-2 fs-6"
                            v-model="filters.industryCategory" v-bind="field" :searchable="false"
                            placeholder="Filter by industries" :resolve-on-load="false" :options="industryCategories" />
                    </Field>
                </div>
                <div class="col-12 col-md-3 col-xl-2 mt-2">
                    <button type="button"
                        class="btn btn-light d-flex justify-content-center align-items-center py-auto w-100"
                        data-toggle="button" aria-pressed="false" autocomplete="off" @click="applyFilter">
                        Find Employers
                    </button>
                </div>
            </div>
        </div>


        <div class="d-flex flex-wrap flex-stack py-7">
            <div  class="d-flex flex-wrap align-items-center justify-content-center gap-4 my-1">
                <h3 class="fw-bold my-0 text-center">
                    <!-- <span v-if="loading">Loading...</span> -->
                    <!-- <span v-else>{{ paginatedCourses.length }} Courses Found</span> -->
                    <span>{{ pagination.total }} {{ pagination.total > 1 ? "companies" : 'company' }} Found</span>
                </h3>
                <p  v-if="pagination.total >= 1" class="my-0 p-5 fs-5 text-center text-gray-600 card">
                 <span>Recommended for you<i class="fa-solid fa-chevron-down mx-5"></i></span>
                </p>
            </div>
            <div class="d-flex flex-wrap my-1">
                <p class="mt-3">VIEW JOBS IN BLOCKS, ROWS OR MAP VIEW</p>
                <ul class="nav nav-pills me-6 mb-2 mb-sm-0 mx-4">
                    <li class="nav-item m-0">
                        <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary me-3 active"
                            data-bs-toggle="tab" href="#kt_project_users_card_pane" @click="currentViewTab = 'card'">
                            <span class="svg-icon svg-icon-2"> <svg xmlns="http://www.w3.org/2000/svg" width="24px"
                                    height="24px" viewBox="0 0 24 24">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="5" y="5" width="5" height="5" rx="1" fill="currentColor" />
                                        <rect x="14" y="5" width="5" height="5" rx="1" fill="currentColor"
                                            opacity="0.3" />
                                        <rect x="5" y="14" width="5" height="5" rx="1" fill="currentColor"
                                            opacity="0.3" />
                                        <rect x="14" y="14" width="5" height="5" rx="1" fill="currentColor"
                                            opacity="0.3" />
                                    </g>
                                </svg>
                            </span>
                        </a>
                    </li>

                    <li class="nav-item m-0">
                        <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary me-3"
                            data-bs-toggle="tab" href="#kt_project_users_table_pane" @click="currentViewTab = 'table'">
                            <span class="svg-icon svg-icon-2"> <svg width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z"
                                        fill="currentColor" />
                                    <path opacity="0.3"
                                        d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14Z"
                                        fill="currentColor" />
                                    <path opacity="0.3"
                                        d="M21 21H3C2.4 21 2 20.6 2 20V18C2 17.4 2.4 17 3 17H21C21.6 17 22 17.4 22 18V20C22 20.6 21.6 21 21 21Z"
                                        fill="currentColor" />
                                </svg>
                            </span>
                        </a>
                    </li>

                    <li class="nav-item m-0">
                        <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary" data-bs-toggle="tab"
                            href="#kt_project_users_map_pane" @click="currentViewTab = 'map'">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" id="IconChangeColor"
                                height="16" width="16">
                                <path
                                    d="M168.3 499.2C116.1 435 0 279.4 0 192C0 85.96 85.96 0 192 0C298 0 384 85.96 384 192C384 279.4 ***********.7 499.2C203.4 514.5 180.6 514.5 168.3 499.2H168.3zM192 256C227.3 ***********.3 256 192C256 156.7 227.3 128 192 128C156.7 ***********.7 128 192C128 227.3 156.7 256 192 256z"
                                    id="mainIconPathAttribute" fill="#737373" stroke-width="0.2" stroke="#ff0000">
                                </path>
                            </svg>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- <div v-if="isCoursesEmpty" class="text-center py-10">
            <div class="mb-5">Looks like you don't have any courses yet.</div>
            <div class="mb-5">To add a course either click ‘Add Course’ or ‘Contact Us’ for more information.</div>
            <button class="btn btn-light" @click="onContactUs">Contact Us</button>
        </div> -->
        <!-- v-if="!isCoursesEmpty" -->

        <div class="tab-content">
            <BlockView v-if="currentViewTab === 'card'" :companies="paginatedCourses" :loading="loading" />
            <ListView v-if="currentViewTab === 'table'" :companies="paginatedCourses" :loading="loading" />
            <MapView v-if="currentViewTab === 'map'" :companies="paginatedCourses" :loading="loading" />
            <div  class="d-flex justify-content-end py-10" v-if="!loading && pagination.total > 0 && currentViewTab !== 'map'">
                <TablePagination :total-pages="pagination.last_page" :total="pagination.total"
                    :per-page="pagination.per_page" :current-page="pagination.current_page"
                    @page-change="onPageChange" />
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from 'vue';
import BlockView from "@/components/employer/search/BlockView.vue";
import ListView from "@/components/employer/search/ListView.vue";
import MapView from "@/components/employer/search/MapView.vue";
import TablePagination from "@/components/kt-datatable/table-partials/table-content/table-footer/TablePagination.vue";
import axios from 'axios';
import {
    Field
} from "vee-validate";
import Multiselect from '@vueform/multiselect';

export default defineComponent({
    components: {
        BlockView,
        ListView,
        MapView,
        TablePagination,
        Field,
        Multiselect,
    },


    setup() {
        const banner = ref({
            'trailer_video': null,
            'video': null,
            'imagefullpath': null,
        });
        const students = ref<Student[]>([]);
        const loading = ref(false);
        const searchQuery = ref('');
        const locationQuery = ref('');
        const debouncedSearch = ref('');
        const industryCategories = ref<{ value: number; label: string }[]>([]);
        let debounceTimeout: any = null;
        const stateslist = ref();
        const currentTab = ref('all-students');
        const currentViewTab = ref('card');

        const filters = ref({
            industryCategory: "",
        });
        const filteredCourses = ref<any[]>([]);
        const pagination = ref({
            current_page: 1,
            per_page: 8,
            last_page: 1,
            total: 0,
            to: 0,
            from: 0
        });

        const paginatedCourses = ref<any[]>([]);
        // banner.value = {
        //     'trailer_video': null,
        //     'video': null,
        //     'imagefullpath': null,
        // }

        // const fetchBanner = async () => {
        //     try {
        //         const response = await fetch(
        //             'api/getBanner/Employer Pipeline',
        //             {
        //                 method: 'GET',
        //                 headers: {
        //                     'Content-Type': 'application/json',
        //                     'X-Requested-With': 'XMLHttpRequest',
        //                 },
        //             }
        //         );

        //         const data = await response.json();
        //         banner.value = data;
        //     } catch (error) {
        //         console.log(error)
        //     }
        // };

        const fetchCompanies = async (page = 1, filterParams = {}) => {
            loading.value = true;
            try {
                const params = new URLSearchParams();
                if (filterParams.search) params.append('search', filterParams.search);
                if (filterParams.location) params.append('location', filterParams.location);
                if (filterParams.industry) params.append('industry', filterParams.industry);
                params.append('per_page', pagination.value.per_page.toString());
                params.append('page', page.toString());

                const response = await axios.get(`getAllCompanies?${params.toString()}`);
                const data = response.data.data || [];
                const meta = response.data.meta || {};

                paginatedCourses.value = data.map((item: any) => {
                    const company = item || {};
                    const detail = company.detail || {};
                    const industries = company.industries || [];
                    const states = company.states || [];
                    return {
                        id: company.id,
                        name: company.name,
                        logo: detail?.logo_full_path || '',
                        isFav: company.favourite || false,
                        location: states.map((state: any) => state.name),
                        description: industries.map((industry: any) => industry.name).join(' ・ '),
                        profileUrl: `/company/${company.id}`,
                    };
                });

                pagination.value.current_page = meta.current_page || 1;
                pagination.value.last_page = meta.last_page || 1;
                pagination.value.total = meta.total || paginatedCourses.value.length;
                pagination.value.from = meta.from || ((pagination.value.total > 0) ? ((pagination.value.current_page - 1) * pagination.value.per_page + 1) : 0);
                pagination.value.to = meta.to || ((pagination.value.total > 0) ? Math.min(pagination.value.from + pagination.value.per_page - 1, pagination.value.total) : 0);
            } catch (error) {
                paginatedCourses.value = [];
                pagination.value = { current_page: 1, per_page: pagination.value.per_page, last_page: 1, total: 0, from: 0, to: 0 };
            } finally {
                loading.value = false;
            }
        };

        const onPageChange = (page: number) => {
            fetchCompanies(page);
        };

        const applyFilter = () => {
            pagination.value.current_page = 1;

            const filterParams = {
                search: searchQuery.value,
                location: locationQuery.value,
                industry: filters.value.industryCategory,
            };

            fetchCompanies(1, filterParams);
        };

        // const isCoursesEmpty = computed(() => {
        //     return paginatedCourses.value.length === 0 && !loading.value;
        // });

        const fetchStates = async () => {
            const response = await fetch(
                'states',
                {
                }
            );

            const data = await response.json();
            stateslist.value = data.map((item) => {
                return { value: item.id, label: item.name }
            });
        };

        const fetchAllIndustriesCategories = async () => {
            try {
                const response = await axios.get("getAllIndustryCategories");
                const categories = response.data.instriesCatergories || [];

                industryCategories.value = categories.map((item: any) => ({
                    value: item.id,
                    label: item.name,
                }));
            } catch (error) {
                console.error("Error fetching industry categories:", error);
                industryCategories.value = [];
            }
        };

        // When searchQuery changes, debounce and trigger applyFilter
        // watch(searchQuery, (newVal) => {
        //     if (debounceTimeout) clearTimeout(debounceTimeout);
        //     debounceTimeout = setTimeout(() => {
        //         debouncedSearch.value = newVal;
        //         applyFilter(); 
        //     }, 500); 
        // });

        const scrollToSection = (sectionId: string) => {
            const section = document.getElementById(sectionId);

            if (section) {
                section.scrollIntoView({ behavior: 'smooth' });
            }
        }

        onMounted(async () => {
            await fetchStates();
            await fetchCompanies();
            await fetchAllIndustriesCategories();
        });

        watch(
            [searchQuery, locationQuery, () => filters.value.industryCategory],
            () => {
                if (debounceTimeout) clearTimeout(debounceTimeout);
                debounceTimeout = setTimeout(() => {
                    // Check if all filters are cleared
                    if (
                        !searchQuery.value &&
                        !locationQuery.value &&
                        (filters.value.industryCategory === null ||
                            filters.value.industryCategory === '' ||
                            filters.value.industryCategory === undefined)
                    ) {
                        fetchCompanies(1, {}); 
                    } 
                }, 500); 
            },
            { deep: true } 
        );

        return {
            banner,
            loading,
            searchQuery,
            locationQuery,
            currentTab,
            currentViewTab,
            scrollToSection,
            industryCategories,
            filters,
            filteredCourses,
            // isCoursesEmpty,
            applyFilter,
            fetchCompanies,
            paginatedCourses,
            pagination,
            onPageChange,
        }
    },
})
</script>

<style scoped>
.banner {
    background-color: #bbb;
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    overflow: hidden;
    min-height: 30vw;
}

.full-view-banner {
    margin-left: -30px;
    margin-right: -30px;
}

.banner-video {
    height: 100%;
}

.banner-video>video {
    width: 101% !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}


.nav-tabs .nav-link {
    border: none !important;
    color: #A1A5B7;
    background-color: transparent !important;
}

.nav-tabs .nav-link:hover {
    color: #000;
    background-color: transparent !important;
}

.nav-tabs .nav-link.active {
    color: #000;
    font-weight: bold;
    border-bottom: 2px solid #000 !important;
    background-color: transparent !important;
    box-shadow: none !important;
}

.btn-white-custom {
    background: #fff;
    color: #000;
}

.btn-border-custom {
    border: 1px solid white !important;
    color: #000;
    background: white;
}

.btn-border-custom:hover {
    border: 0px !important;
    color: white;
    background: #000;
}

.btn-black-custom:hover,
.btn-white-custom {
    background-color: #fff !important;
    color: #000 !important;
}

.btn-black-custom,
.btn-white-custom:hover {
    background-color: #000 !important;
    color: #fff !important;
}

.btn-white-custom:hover,
.btn.btn-white-custom:hover:not(.btn-active) {
    background-color: #000 !important;
    color: #fff !important;
}

@media (max-width: 1280px) {
    .banner {
        height: 95.25vw;

    }

    /* .banner_detail_box {
        left: 40%;
    } */

    .banner-video>video {
        height: 100% !important;
        width: calc(65vw + 65vh) !important;
    }
}


@media (max-width: 991px) {

    .banner {
        height: 90.25vw;
    }


    .full-view-banner,
    .module-sections {
        margin-left: -20px;
        margin-right: -20px;
    }

    .full-view-banner {
        margin-top: 58.16px;
    }

}



@media (max-width: 991px) and (min-width: 768px) and (orientation:portrait) {
    .banner {
        height: 95.25vw;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(66vw + 66vh) !important;
    }
}

@media (max-width: 991px) and (orientation:landscape) {
    .banner-video>video {
        height: auto !important;
        width: calc(70vw + 70vh) !important;
    }
}

@media (max-width: 767px) {

    .banner {
        height: 110vw;
    }

    /* .banner_detail_box {
        left: 50%;
    } */

}

@media (max-width: 575px) {
    .full-view-banner {
        margin-top: 0;
    }

    .banner {
        height: 124vw;
    }

    /* .banner_detail_box {
        width: 70vw !important;
    } */

    .banner-video>video {
        height: 100% !important;
        width: calc(90vw + 90vh) !important;
    }
}
</style>