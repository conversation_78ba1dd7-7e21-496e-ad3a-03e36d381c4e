<?php

namespace App\Http\Controllers;

use App\Course;
use App\Gameplan;
use App\GameplanCompany;
use App\GameplanQuestion;
use App\IndividualStudent;
use App\NonStudentPlan;
use App\Plan;
use App\Student;
use App\User;
use App\Teacher;
use App\Staff;
use Auth;
use Illuminate\Http\Request;
use PDF;
use App\Industryunit;
use App\IndustryCategory;
use App\Lesson;
use App\Scholarship;
use App\Services\UserAccessService;
use App\SkillstrainingTemplate;
use App\WorkexperienceTemplate;

class GameplansController extends Controller
{
    public function index()
    {
        if (Auth::check() && (Auth::user()->isStudent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff()) && session('studentView')))) {
            $breadcrumb = "gameplan";
            $bannerTitle = "Your Game plan";
            // if (Auth::user()->isNonStudent()) {
            //     $currentPlan = NonStudentPlan::where('user_id', Auth::id())->with(['currentIndustry', 'getintoIndustry'])->latest()->first();
            //     $user_id = Auth::id();
            //     return view('gameplans.nonstudent', compact('currentPlan', 'user_id', 'breadcrumb', 'bannerTitle'));
            // } else {

            if (Auth::user()->isTeacher()) {
                $student = Teacher::find(Auth::id());
            } elseif (Auth::user()->isStaff()) {
                $student = Staff::find(Auth::id());
            } else {
                $student = IndividualStudent::find(Auth::id());
                if (!$student) {
                    $student = Student::find(Auth::id());
                }
            }

            $gameplan = Auth::user()->lastPlan();

            // dd($gameplan);

            $instituteDetail = Auth::user()->school?->detail;
            $instituteId = $instituteDetail?->school_id;
            $instituteType = $instituteDetail?->institute_type->value ?? null;

            $questions = GameplanQuestion::with('options')
            ->orderBy('sort_order')
            ->when(Auth::user()->isIndividual(), function ($query) {
                $query->where('question_key', '<>', 'finishing_school');
            })
            ->where(function ($query) use ($instituteType, $instituteId) {
                $query->where('institute_type', $instituteType)
                    ->where(function ($subQuery) use ($instituteId) {
                        $subQuery->whereNull('institute_id')
                                ->orWhere('institute_id', $instituteId);
                    })
                     ->orWhereNull('institute_type');
            })
            ->get();
            

            $currentPlan = Plan::where('user_id', Auth::id())->with(['tafes', 'colleges', 'universities', 'industries'])->latest()->first();

            $user = Auth::user();


            return view('gameplans.student', compact('gameplan', 'questions', 'currentPlan', 'breadcrumb', 'bannerTitle', 'user'));
            // }
        }
        abort(403, 'You do not have permission to perform this action.');
    }

    public function show($id)
    {
        if (Auth::check() && (Auth::user()->isStudent()) || (Auth::user()->isParent()) && UserAccessService::currentUserCanAccess($id) || (Auth::user()->isTeacher()) && UserAccessService::currentUserCanAccess($id) || (Auth::user()->isTeacher()) && UserAccessService::currentUserCanAccess($id) || ((Auth::user()->isEmployer()) && UserAccessService::currentUserCanAccess($id))) {
            $breadcrumb = "gameplan";

            $bannerTitle = (Auth::user()->isParent() || (Auth::user()->isTeacher() && $id) || (Auth::user()->isEmployer() && $id)) ? "Game plan" : "Your Game plan";

            $student = IndividualStudent::find($id);
            if (!$student) {
                $student = Student::find($id);
            }

            $name =  $student->name;
            $gameplan = User::find($id)->lastPlan();

            $instituteDetail = Auth::user()->school?->detail;
            $instituteId = $instituteDetail?->school_id;
            $instituteType = $instituteDetail?->institute_type->value ?? null;

            $questions = GameplanQuestion::with('options')
            ->orderBy('sort_order')
            ->where(function ($query) use ($instituteType, $instituteId) {
                $query->where('institute_type', $instituteType)
                    ->where(function ($subQuery) use ($instituteId) {
                        $subQuery->whereNull('institute_id')
                                ->orWhere('institute_id', $instituteId);
                    });
            })
            ->orWhereNull('institute_type')
            ->get();

            $currentPlan = Plan::where('user_id', $id)->with(['tafes', 'colleges', 'universities', 'industries'])->latest()->first();
            return view('gameplans.student', compact('gameplan', 'questions', 'currentPlan', 'name', 'breadcrumb', 'bannerTitle', 'id'));
        }
        abort(403, 'You do not have permission to perform this action.');
    }


    public function studentGameplan($id)
    {
        if (!($id && (User::findOrfail($id)->isStudent()) && UserAccessService::currentUserCanAccess($id))) {
            abort(403, 'You do not have permission to perform this action.');
        }

        // if (User::find($id)->isNonStudent()) {
        //     $currentPlan = NonStudentPlan::where('user_id', $id)->with(['currentIndustry', 'getintoIndustry'])->latest()->first();
        //     if ($currentPlan) {
        //         $user_id = $id;
        //         $name = User::find($id)->name;
        //         return view('gameplans.parents.nonstudent', compact('currentPlan', 'user_id', 'name'));
        //     }
        // }

        $user = IndividualStudent::find($id);
        if (!$user) {
            $user = Student::find($id);
        }
        // $gamesubjects = $user->subjects()->get();
        // $gamecourses = $user->courses()->get();
        // $gamescholarships = $user->scholarships()->get();
        // $gamecompanies = $user->companies()->get();
        $currentPlan = Plan::where('user_id', $id)->with(['tafes', 'colleges', 'universities', 'industries'])->latest()->first();
        // if (!$currentPlan) {
        //     $currentPlan = NonStudentPlan::where('user_id', $id)->with(['currentIndustry', 'getintoIndustry'])->latest()->first();
        //     if ($currentPlan) {
        //         $user_id = $id;
        //         $name = User::find($id)->name;
        //         return view('gameplans.parents.nonstudent', compact('currentPlan', 'user_id', 'name'));
        //     }
        // }
        $name = $user->name;

        return view('gameplans.parents.student', compact(/* 'gamesubjects', 'gamecourses', 'gamescholarships', 'gamecompanies',  */'currentPlan', 'id', 'name'));
    }

    public function parentDownloadPDF(Request $request)
    {
        $user = User::find($request->id);
        $individual = IndividualStudent::find($request->id);
        if ($individual && $individual->isNonStudent()) {
        } else {
            return $this->studentPDF($user->id, $user->name);
        }

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl);
    }

    public function store(Request $request)
    {
        $success = $this->storeGameplan($request);
        if ($success && $request->has('download')) {
            return $this->studentPDF(Auth::id(), Auth::user()->name);
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Game plan saved successfully!');
    }

    public function storeGameplan($request)
    {
        $user_id = Auth::id();
        $subjects = $request->subjects;
        $scholarships = $request->scholarships;
        $courses = $request->courses;
        $institutes = $request->institutes;
        $companies = $request->companies;

        if (Auth::user()->isTeacher()) {
            $student = Teacher::find(Auth::id());
        } elseif (Auth::user()->isStaff()) {
            $student = Staff::find(Auth::id());
        } else {
            $student = IndividualStudent::find($user_id);

            if (!$student) {
                $student = Student::find($user_id);
            }
        }
        if ($subjects) {
            $student->subjects()->sync($subjects);
        }
        if ($scholarships) {
            $student->scholarships()->sync($scholarships);
        }
        if ($courses) {
            $student->courses()->sync($courses);
        }
        $gcompany = collect();
        if ($companies) {
            foreach ($companies as $company) {
                if ($company) {
                    $gcompany->push(GameplanCompany::firstOrNew(['company' => $company]));
                }
            }
            $student->companies()->saveMany($gcompany);
            $student->companies()->whereNotIn(
                'id',
                $gcompany->pluck('id')
            )->delete();
        }
        return (true);
    }

    public function studentPDF($user_id, $user_name)
    {
        $student = IndividualStudent::find($user_id);
        if (!$student) {
            $student = Student::find($user_id);
        }
        $gamesubjects = $student->subjects()->get();
        $gamescholarships = $student->scholarships()->get();
        $gamecourses = $student->courses()->select('course_id', 'title', 'institution_id', 'institution_type')->get();
        $gamecompanies = $student->companies()->get();

        foreach ($gamecourses as $gamecourse) {
            $gamecourse->institution;
        }

        $currentPlan = Plan::where('user_id', $user_id)->with(['tafes', 'colleges', 'universities', 'industries'])->latest()->first();
        $currentPlan->studyplaces = collect();
        if ($currentPlan->tafes) {
            foreach ($currentPlan->tafes as $tafe) {
                $currentPlan->studyplaces->push($tafe->name);
            }
        }
        if ($currentPlan->universities) {
            foreach ($currentPlan->universities as $university) {
                $currentPlan->studyplaces->push($university->name);
            }
        }
        if ($currentPlan->colleges) {
            foreach ($currentPlan->colleges as $college) {
                $currentPlan->studyplaces->push($college->name);
            }
        }
        if ($currentPlan->InstituteOther) {
            foreach ($currentPlan->InstituteOther as $other) {
                $currentPlan->studyplaces->push($other);
            }
        }
        view()->share(['gamesubjects' => $gamesubjects, 'gamecourses' => $gamecourses, 'gamescholarships' => $gamescholarships, 'gamecompanies' => $gamecompanies, 'currentPlan' => $currentPlan, 'userName' => $user_name]);
        $pdf = PDF::loadView('gameplans.pdf.student');
        return $pdf->stream();
        // return $pdf->download($user_name . '-gameplan.pdf');
    }

    public function nonStudentPDF(Request $request)
    {
        $user = User::find($request->id);
        $currentPlan = NonStudentPlan::where('user_id', $request->id)->with(['currentIndustry', 'getintoIndustry'])->latest()->first();
        view()->share(['currentPlan' => $currentPlan, 'userName' => $user->name]);
        $pdf = PDF::loadView('gameplans.pdf.nonstudent');
        return $pdf->stream();
        // return $pdf->download($user->name . '-gameplan.pdf');
    }

    public function studentSubjects($id = '')
    {
        if (Auth::check() && (Auth::user()->isStudent() || Auth::user()->isParent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff()) && (session('studentView') || $id)))) {
            $breadcrumb = "studentSubjects";
            $bannerTitle = Auth::user()->isParent() ? "Subjects" : "Your Subjects";

            if (!$id) {
                $user = Auth::id();
            } else {
                if (UserAccessService::currentUserCanAccess($id)) {
                    $user = $id;
                } else {
                    abort(403, 'You do not have permission to perform this action.');
                }
            }

            if (Auth::user()->isTeacher()) {
                $student = Teacher::findOrFail(Auth::id());
            } elseif (Auth::user()->isStaff()) {
                $student = Staff::findOrFail(Auth::id());
            } else {
                $student = IndividualStudent::find($user);
                if (!$student) {
                    $student = Student::findOrFail($user);
                }
            }


            $gamesubjects = $student->subjects()->get();
            $name =  $student->name;

            return view('gameplans.studentSubjects', compact('name', 'id', 'gamesubjects', 'breadcrumb', 'bannerTitle'));
            // }
        }
        abort(403, 'You do not have permission to perform this action.');
    }

    public function subjectsStore(Request $request)
    {
        $user_id = Auth::id();

        if (Auth::user()->isTeacher()) {
            $student = Teacher::find(Auth::id());
        } elseif (Auth::user()->isStaff()) {
            $student = Staff::find(Auth::id());
        } else {
            $student = IndividualStudent::find($user_id);

            if (!$student) {
                $student = Student::find($user_id);
            }
        }
        $student->subjects()->sync($request->subjects);

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Subjects saved successfully!');
    }


    public function studentCourses($id = '')
    {
        if (Auth::check() && (Auth::user()->isStudent() || Auth::user()->isParent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff() || Auth::user()->isEmployer()) && (session('studentView') || $id)))) {
            $breadcrumb = "studentCourses";
            $bannerTitle = (Auth::user()->isParent() || (Auth::user()->isTeacher() && $id) || (Auth::user()->isTeacher() && $id)) ? "Courses" : "Your Courses";

            if (!$id) {
                $user = Auth::id();
            } else {
                if (UserAccessService::currentUserCanAccess($id)) {
                    $user = $id;
                } else {
                    abort(403, 'You do not have permission to perform this action.');
                }
            }

            if (!User::find($user)->hasCourseFinderAccess()) {   // check if user has access
                abort(403, 'You do not have permission to perform this action.');
            }

            if (Auth::user()->isTeacher() && !$id) {
                $student = Teacher::findOrFail(Auth::id());
            } elseif (Auth::user()->isStaff()) {
                $student = Staff::findOrFail(Auth::id());
            } else {
                $student = IndividualStudent::find($user);
                if (!$student) {
                    $student = Student::findOrFail($user);
                }
            }

            $gamecourses = $student->courses()->get();
            $name =  $student->name;

            return view('gameplans.studentCourses', compact('name', 'breadcrumb', 'bannerTitle', 'id', 'gamecourses'));
        }
        abort(403, 'You do not have permission to perform this action.');
    }

    public function coursesStore(Request $request)
    {
        $user_id = Auth::id();

        if (Auth::user()->isTeacher()) {
            $student = Teacher::find(Auth::id());
        } elseif (Auth::user()->isStaff()) {
            $student = Staff::find(Auth::id());
        } else {
            $student = IndividualStudent::find($user_id);

            if (!$student) {
                $student = Student::find($user_id);
            }
        }
        $student->courses()->sync($request->courses);

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Courses saved successfully!');
    }

    public function studentScholarships($id = '')
    {
        if (Auth::check() && (Auth::user()->isStudent() || Auth::user()->isParent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff() ||  Auth::user()->isEmployer()) && (session('studentView') || $id)))) {
            $breadcrumb = "studentScholarships";
            $bannerTitle = (Auth::user()->isParent() || (Auth::user()->isTeacher() && $id) || (Auth::user()->isEmployer() && $id)) ? "Scholarships" : "Your Scholarships";

            if (!$id) {
                $user = Auth::id();
            } else {
                if (UserAccessService::currentUserCanAccess($id)) {
                    $user = $id;
                } else {
                    abort(403, 'You do not have permission to perform this action.');
                }
            }

            if (!User::find($user)->hasScholarshipFinderAccess()) {   // check if user has access
                abort(403, 'You do not have permission to perform this action.');
            }

            if (Auth::user()->isTeacher() && !$id) {
                $student = Teacher::find(Auth::id());
            } elseif (Auth::user()->isStaff()) {
                $student = Staff::find(Auth::id());
            } else {
                $student = IndividualStudent::find($user);
                if (!$student) {
                    $student = Student::find($user);
                }
            }

            $gamescholarships = User::find($student->id)->getFavoriteItems(Scholarship::class)->select('id', 'scholarship_provider_id', 'picked', 'imagepath', 'name', 'date', 'url','amount')
            ->with('locations', 'provider:id,name')->get();
            $name =  $student->name;

            return view('gameplans.studentScholarships', compact('name', 'breadcrumb', 'bannerTitle', 'id', 'gamescholarships'));
        }
        abort(403, 'You do not have permission to perform this action.');
    }

    public function studentContent($id = '')
    {
        if (Auth::check() && (Auth::user()->isStudent() || Auth::user()->isParent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff() || Auth::user()->isEmployer()) && (session('studentView') || $id)))) {

            $breadcrumb = "studentContent";
            $bannerTitle = (Auth::user()->isParent() || (Auth::user()->isTeacher() && $id)) ? "Content" : "Your Content";

            if (!$id) {
                $user = Auth::id();
            } else {
                if (UserAccessService::currentUserCanAccess($id)) {
                    $user = $id;
                } else {
                    abort(403, 'You do not have permission to perform this action.');
                }
            }

            if (!User::find($user)->hasIndustriesAccess()) {   // check if user has access to content
                abort(403, 'You do not have permission to perform this action.');
            }

            if (Auth::user()->isTeacher() && !$id) {
                $student = Teacher::find(Auth::id());
            } elseif (Auth::user()->isStaff()) {
                $student = Staff::find(Auth::id());
            } else {
                $student = IndividualStudent::find($user);
                if (!$student) {
                    $student = User::find($user);
                }
            }

            // $unitUsers = DB::table('industryunit_user')->pluck('industryunit_id', 'user_id');
            // foreach($unitUsers as $key=> $unit){
            //     $user = User::find($key);
            //     $unit = Industryunit::find($unit);
            //     if($user && $unit){
            //         $user->toggleFavorite($unit);
            //     }
            // }

            // $industries = $student->industryunits()->get();
            $industries = $student->getFavoriteItems(IndustryCategory::class)->get();
            $units = $student->getFavoriteItems(Industryunit::class)->get();
            $skillstrainings = $student->getFavoriteItems(SkillstrainingTemplate::class)->get();
            $vwes = $student->getFavoriteItems(WorkexperienceTemplate::class)->get();
            $lessons = $student->getFavoriteItems(Lesson::class)->get();
            $name =  $student->name;
            return view('gameplans.studentContent', compact('industries', 'units', 'skillstrainings', 'vwes', 'lessons', 'breadcrumb', 'bannerTitle', 'id', 'name'));
            // }
        }
        abort(403, 'You do not have permission to perform this action.');
    }

    public function getGameplanCourses(Request $request)
    {
        $courses = Course::with('feeType')
            ->when($request->title, function ($query) use ($request) {
                return $query->where('title', 'like', '%' . $request->title . '%');
            })
            ->when($request->institute, function ($query) use ($request) {
                return $query->where(function ($qu) use ($request) {
                    return $qu->orWhereHas('tafeinstitution', function ($q) use ($request) {
                        $q->where('name', 'like', '%' . $request->institute . '%');
                    })->orWhereHas('collegeinstitution', function ($q) use ($request) {
                        $q->where('name', 'like', '%' . $request->institute . '%');
                    })->orWhereHas('universityinstitution', function ($q) use ($request) {
                        $q->where('name', 'like', '%' . $request->institute . '%');
                    })->orWhereHas('rtoinstitution', function ($q) use ($request) {
                        $q->where('name', 'like', '%' . $request->institute . '%');
                    });
                });
            })
            ->get();
        // $courses = Course::with('feeType')->where(function ($query) {
        //     foreach (explode(' ', request('term')) as $term) {
        //         $query->where('title', 'like', '%' . $term . '%');
        //     }
        // })->get();

        foreach ($courses as $key => $course) {
            $course->institution;
        }

        $returnHTML = view('gameplans.courseslist', compact('courses'))->render();
        return response()->json(array('success' => true, 'html' => $returnHTML));

        return $courses;
    }
}
