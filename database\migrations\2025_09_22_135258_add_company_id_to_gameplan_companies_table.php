<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gameplan_companies', function (Blueprint $table) {
            $table->unsignedBigInteger('company_id')->nullable()->after('gameplan_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gameplan_companies', function (Blueprint $table) {
            $table->dropColumn('company_id');
        });
    }
};
