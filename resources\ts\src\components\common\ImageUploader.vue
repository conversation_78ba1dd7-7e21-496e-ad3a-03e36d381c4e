<template>
    <div>
        <!-- Image input -->
        <div 
            ref="imageInputEl" 
            :class="['image-input image-input-outline', !preview ? 'image-input-empty' : '']" 
            data-kt-image-input="true" 
            :style="{
                backgroundImage: backgroundStyle,
            }"
        >
            <!-- Preview -->
            <div class="image-input-wrapper w-150px h-150px" :style="{ backgroundImage: wrapperBackgroundStyle }"></div>

            <!-- Change label -->
            <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip"
                title="Change image">
                <i class="bi bi-pencil-fill fs-7"></i>
                <input
                    type="file"
                    :name="name"
                    :accept="accept"
                    @change="onFileChange"
                    @blur="handleBlur"
                    ref="fileInput"
                    :disabled="disabled"
                />
                <input type="hidden" :name="name + '_remove'" />
            </label>

            <!-- Remove button -->
           <span
               v-if="showRemoveButton"
               :class="['btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow custom-image-uploader-remove-btn', disabled ? 'disabled' : '']"
               data-bs-toggle="tooltip"
               @click="removeImage"
               title="Remove image"
           >
                <i class="bi bi-x fs-2"></i>
            </span>
        </div>

        <!-- error message -->
        <div class="text-danger small w-100 mt-3">{{ errorMessage }}</div>
        
        <!-- Hint -->
        <div class="form-text">{{ helpText }}</div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { useField } from 'vee-validate'
import { ImageInputComponent, defaultImageInputOptions, defaultImageInputQueires } from '@/assets/ts/components'
import type { PropType } from 'vue'

// Props
const props = defineProps({
    defaultImage: {
        type: String,
        default: '/media/logos/blank-image.svg',
    },
    name: {
        type: String,
        required: true,
    },
    helpText: {
        type: String,
        default: '',
    },
    accept: {
        type: String,
        default: '',
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    rules: {
        type: [String, Object] as PropType<any>,
        default: '',
    }
})

// VeeValidate useField
const { errorMessage, handleChange, handleBlur, value } = useField(props.name, props.rules);

// Refs
const imageInputEl = ref<HTMLElement | null>(null)
const fileInput = ref<HTMLInputElement | null>(null)
const preview = ref<string | null>(null)
let imageInputInstance: ImageInputComponent | undefined

// Initialize Metronic component
onMounted(() => {
    if (imageInputEl.value) {
        imageInputInstance = new ImageInputComponent(
            imageInputEl.value,
            defaultImageInputOptions,
            defaultImageInputQueires
        )
    }
})

const backgroundStyle = computed(() => {
  if (preview.value) {
    return `url(${preview.value})`;
  }
  if (typeof value.value === 'string' && value.value) {
    return `url(${value.value})`;
  }
  if (props.defaultImage) {
    return `url(${props.defaultImage})`;
  }
  return 'none';
})

const wrapperBackgroundStyle = computed(() => {
  if (preview.value) {
    return `url(${preview.value})`;
  }
  if (typeof value.value === 'string' && value.value) {
    return `url(${value.value})`;
  }
  return '';
})

const showRemoveButton = computed(() => {
  return !!preview.value || (typeof value.value === 'string' && value.value);
})

// Handlers
function onFileChange(e: Event) {
    const input = e.target as HTMLInputElement
    if (input.files && input.files[0]) {
        const file = input.files[0]

        // Preview
        const reader = new FileReader()
        reader.onload = (ev) => {
            preview.value = ev.target?.result as string
        }
        reader.readAsDataURL(file)

        handleChange(file) // ✅ pass File to VeeValidate
    }
}

function removeImage() {
    preview.value = null
    if (fileInput.value) fileInput.value.value = ''
    handleChange(null) // ✅ clear in VeeValidate
}
</script>

<style scoped lang="scss">
.image-input-wrapper {
    background-size: cover;
    background-position: center;
    border-radius: 8px;
}
.custom-image-uploader-remove-btn {
    position: absolute;
    left: 100%;
    top: 100%;
    transform: translate(-50%, -50%);
}
</style>