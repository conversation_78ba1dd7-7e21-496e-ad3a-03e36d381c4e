<template>
  <!-- <PERSON><PERSON> and <PERSON> (Jobs & Opportunities only) -->
  <div v-if="fromCompanyPage" class="card">
    <div class="card-header border-0 px-xl-0">
      <div class="card-title fs-1">
        Jobs & Opportunities
      </div>
    </div>
    <div class="card-body fs-4 px-xl-0 p-xl-0">
      <p>Browse job openings or explore the map to find opportunities near you.</p>
    </div>
  </div>
  <div v-if="fromCompanyPage" class="card">
    <div class="card-body p-xl-0 row justify-content-start align-items-center">
      <div class="col-12 col-xl-3 mt-2 position-relative">
        <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
          <svg width="10" height="10" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11.7429 10.3431C12.7112 9.02181 13.1449 7.38361 12.9572 5.75627C12.7695 4.12893 11.9743 2.63246 10.7307 1.56625C9.48701 0.500045 7.88665 -0.0572725 6.24973 0.00580065C4.61282 0.0688738 3.06008 0.747686 1.90217 1.90643C0.744249 3.06518 0.0665484 4.6184 0.00464653 6.25536C-0.0572553 7.89231 0.501207 9.49228 1.56831 10.7352C2.6354 11.9781 4.13244 12.7722 5.75992 12.9587C7.38739 13.1452 9.02528 12.7104 10.3459 11.7411H10.3449C10.3742 11.7811 10.4069 11.8195 10.4429 11.8561L14.2929 15.7061C14.4804 15.8938 14.7348 15.9992 15 15.9993C15.2653 15.9994 15.5198 15.8941 15.7074 15.7066C15.895 15.5191 16.0005 15.2647 16.0006 14.9995C16.0007 14.7342 15.8954 14.4798 15.7079 14.2921L11.8579 10.4421C11.8222 10.4059 11.7837 10.3735 11.7429 10.3431ZM12.0009 6.49912C12.0009 7.22139 11.8586 7.93659 11.5822 8.60388C11.3058 9.27117 10.9007 9.87749 10.39 10.3882C9.87926 10.8989 9.27295 11.3041 8.60566 11.5805C7.93837 11.8569 7.22317 11.9991 6.5009 11.9991C5.77863 11.9991 5.06343 11.8569 4.39614 11.5805C3.72885 11.3041 3.12253 10.8989 2.61181 10.3882C2.10109 9.87749 1.69596 9.27117 1.41956 8.60388C1.14316 7.93659 1.0009 7.22139 1.0009 6.49912C1.0009 5.04043 1.58036 3.64149 2.61181 2.61004C3.64326 1.57859 5.04221 0.999124 6.5009 0.999124C7.95959 0.999124 9.35853 1.57859 10.39 2.61004C11.4214 3.64149 12.0009 5.04043 12.0009 6.49912Z"
              fill="#606060" />
          </svg>
        </span>
        <input v-model="searchQuery" @keyup.enter="onSearch" class="form-control form-control-solid ps-10 py-4"
          type="text" placeholder="Search keywords" />
      </div>
      <div v-if="fromCompanyPage" class="col-12 col-xl-3 mt-2">
        <Multiselect v-model="filters.location" :options="locations" placeholder="Select Location"
          class="form-control form-control-solid py-3 fs-6" :searchable="false" />
      </div>
      <div class="col-12 col-xl-3 mt-2">
        <Multiselect v-model="filters.courseType" :options="courseTypes" placeholder="Filter by industries"
          class="form-control form-control-solid py-3 fs-6" :searchable="false" />
      </div>
      <div class="col-12 col-xl-2 mt-2">
        <button type="button" class="btn btn-light d-flex justify-content-center align-items-center py-4 w-100"
          @click="applyFilter">
          Search
        </button>
      </div>
    </div>
  </div>

  <!-- Tabs (Jobs & Opportunities only) -->
  <div v-if="fromCompanyPage" class="d-flex flex-wrap flex-stack py-5 px-lg-0 px-sm-10">
    <div class="d-flex flex-wrap align-items-center justify-content-center gap-4 my-1">
      <p class="fw-bold fs-4">{{ total }} items Found</p>
    </div>
    <div class="d-flex flex-wrap my-1">
      <ul class="nav nav-pills me-6 mb-2 mb-sm-0">
        <li class="nav-item m-0">
          <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary me-3 active"
            data-bs-toggle="tab" href="#kt_project_users_card_pane" @click="currentViewTab = 'card'">
            <span class="svg-icon svg-icon-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24">
                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                  <rect x="5" y="5" width="5" height="5" rx="1" fill="currentColor" />
                  <rect x="14" y="5" width="5" height="5" rx="1" fill="currentColor" opacity="0.3" />
                  <rect x="5" y="14" width="5" height="5" rx="1" fill="currentColor" opacity="0.3" />
                  <rect x="14" y="14" width="5" height="5" rx="1" fill="currentColor" opacity="0.3" />
                </g>
              </svg>
            </span>
          </a>
        </li>
        <li class="nav-item m-0">
          <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary" data-bs-toggle="tab"
            href="#kt_project_users_map_pane" @click="currentViewTab = 'map'">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" id="IconChangeColor" height="16" width="16">
              <path
                d="M168.3 499.2C116.1 435 0 279.4 0 192C0 85.96 85.96 0 192 0C298 0 384 85.96 384 192C384 279.4 267 435 215.7 499.2C203.4 514.5 180.6 514.5 168.3 499.2H168.3zM192 256C227.3 256 256 227.3 256 192C256 156.7 227.3 128 192 128C156.7 128 128 156.7 128 192C128 227.3 156.7 256 192 256z"
                id="mainIconPathAttribute" fill="#737373" stroke-width="0.2" stroke="#ff0000">
              </path>
            </svg>
          </a>
        </li>
      </ul>
    </div>
  </div>

 
  <div  class="row g-10">
    <div v-if="!validJobs.length && !loading" class="col-12">
      <p class="text-center fs-4 text-muted">{{ noJobsMessage }}</p>
    </div>
    <div v-else-if="validJobs.length" v-for="job in validJobs" :key="job.id || `temp-id-${Math.random().toString(36).substr(2, 9)}`" class="col-12" :class="fromCompanyPage ? 'col-xl-6' : 'col-xl-4'">
      <div class="card mt-5 border">
        <div class="card-header border-0 align-content-center pb-4 pt-7">
          <p class="mb-0 text-muted mt-5">{{ formatDate(job.posted || job.created_at || new Date().toISOString()).text }}</p>
          <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="25" cy="25" r="20" fill="black" />
            <svg width="45" height="40" viewBox="-2 -4 34 33" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M17 11.1388C20.8834 7.18512 30.5926 14.1037 17 23C3.40738 14.1046 13.1166 7.18512 17 11.1388Z"
                fill="white" />
            </svg>
          </svg>
        </div>
        <div class="card-header border-0">
          <div class="card-title flex-column">
            <h3 class="mb-5">{{ job.company || 'Unknown Company' }}</h3>
            <h1>{{ job.title || 'Untitled Job' }}</h1>
          </div>
          <div v-if="!fromCompanyPage" class="bg-light card-div" style="width: 138px; height: 60px;"></div>
        </div>
        <div class="card-body">
          <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
            <a href="#" class="d-flex align-items-center text-gray-700 text-hover-primary me-10 mb-2">
              <svg class="me-2" width="14" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M6 16C6 16 12 10.314 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 2.37122e-08 4.4087 0 6C0 10.314 6 16 6 16ZM6 9C5.20435 9 4.44129 8.68393 3.87868 8.12132C3.31607 7.55871 3 6.79565 3 6C3 5.20435 3.31607 4.44129 3.87868 3.87868C4.44129 3.31607 5.20435 3 6 3C6.79565 3 7.55871 3.31607 8.12132 3.87868C8.68393 4.44129 9 5.20435 9 6C9 6.79565 8.68393 7.55871 8.12132 8.12132C7.55871 8.68393 6.79565 9 6 9Z"
                  fill="#606060" />
              </svg>
              {{ job.location || 'Unknown Location' }}
            </a>
            <a href="#" class="d-flex align-items-center text-gray-700 text-hover-primary me-10 mb-2">
              <svg class="me-2" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M16 8.5C16 10.6217 15.1571 12.6566 13.6569 14.1569C12.1566 15.6571 10.1217 16.5 8 16.5C5.87827 16.5 3.84344 15.6571 2.34315 14.1569C0.842855 12.6566 0 10.6217 0 8.5C0 6.37827 0.842855 4.34344 2.34315 2.84315C3.84344 1.34285 5.87827 0.5 8 0.5C10.1217 0.5 12.1566 1.34285 13.6569 2.84315C15.1571 4.34344 16 6.37827 16 8.5ZM8 4C8 3.86739 7.94732 3.74021 7.85355 3.64645C7.75979 3.55268 7.63261 3.5 7.5 3.5C7.36739 3.5 7.24021 3.55268 7.14645 3.64645C7.05268 3.74021 7 3.86739 7 4V9.5C7.00003 9.58813 7.02335 9.67469 7.06761 9.75091C7.11186 9.82712 7.17547 9.89029 7.252 9.934L10.752 11.934C10.8669 11.9961 11.0014 12.0108 11.127 11.9749C11.2525 11.9391 11.3591 11.8556 11.4238 11.7422C11.4886 11.6288 11.5065 11.4946 11.4736 11.3683C11.4408 11.2419 11.3598 11.1334 11.248 11.066L8 9.21V4Z"
                  fill="#606060" />
              </svg>
              {{ job.type || job.employment_type || 'N/A' }}
            </a>
            <a href="#" class="d-flex align-items-center text-gray-700 text-hover-primary me-10 mb-2">
              <svg class="me-2" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M0 4.5C0 4.23478 0.105357 3.98043 0.292893 3.79289C0.48043 3.60536 0.734784 3.5 1 3.5H15C15.2652 3.5 15.5196 3.60536 15.7071 3.79289C15.8946 3.98043 16 4.23478 16 4.5V12.5C16 12.7652 15.8946 13.0196 15.7071 13.2071C15.5196 13.3946 15.2652 13.5 15 13.5H1C0.734784 13.5 0.48043 13.3946 0.292893 13.2071C0.105357 13.0196 0 12.7652 0 12.5V4.5ZM3 4.5C3 5.03043 2.78929 5.53914 2.41421 5.91421C2.03914 6.28929 1.53043 6.5 1 6.5V10.5C1.53043 10.5 2.03914 10.7107 2.41421 11.0858C2.78929 11.4609 3 11.9696 3 12.5H13C13 11.9696 13.2107 11.4609 13.5858 11.0858C13.9609 10.7107 14.4696 10.5 15 10.5V6.5C14.4696 6.5 13.9609 6.28929 13.5858 5.91421C13.2107 5.53914 13 5.03043 13 4.5H3z"
                  fill="#606060" />
                <path
                  d="M8 10.5C8.53043 10.5 9.03914 10.2893 9.41421 9.91421C9.78929 9.53914 10 8.5 10 7.96957 9.78929 7.46086 9.41421 7.08579 9.03914 6.71071 8.66421 6.33579 8.03043 6.5 8 6.5C7.46957 6.5 6.96086 6.71071 6.58579 7.08579C6.21071 7.46086 6 7.96957 6 8.5C6 9.03043 6.21071 9.53914 6.58579 9.91421C6.96086 10.2893 7.46957 10.5 8 10.5Z"
                  fill="#606060" />
              </svg>
              {{ job.pay_frequency || 'N/A' }}
            </a>
          </div>
        </div>
        <div class="d-flex flex-wrap justify-content-between fw-semibold fs-6 mb-6 px-10">
          <button class="border-0 px-10 py-4 rounded fs-6 bg-light transition">
            View Job
          </button>
          <button class="border-0 px-14 py-4 rounded fs-6 bg-grey transition">
            Apply
          </button>
        </div>
      </div>
    </div>
    <div v-else class="col-12">
      <p class="text-center fs-4 text-muted">Loading jobs...</p>
    </div>
  </div>

 
  <div v-if="isJobFinder && total > 0" class="d-flex justify-content-end py-10">
    <TablePagination :total-pages="lastPage" :total="total" :per-page="perPage" :current-page="currentPage"
      @page-change="onPageChange" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';
import TablePagination from '@/components/kt-datatable/table-partials/table-content/table-footer/TablePagination.vue';
import Multiselect from '@vueform/multiselect';
import { Field } from 'vee-validate';
import { debounce } from 'lodash';
import axios from 'axios';

export default defineComponent({
  components: {
    Multiselect,
    Field,
    TablePagination,
  },
  props: {
    jobs: {
      type: Object,
      default: () => ({
        data: [],
        meta: {
          total: 0,
          current_page: 1,
          per_page: 6,
          last_page: 1,
          from: 1,
          to: 4,
        },
      }),
    },
    noSearchResult: {
      type: Boolean,
      default: false,
    },
    noJobsLeft: {
      type: Boolean,
      default: false,
    },
    fromCompanyPage: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['page-change', 'search'],
  setup(props, { emit }) {
    const searchQuery = ref('');
    const filters = ref({
      location: '',
      courseType: '',
    });
    const locations = ref(['Sydney', 'Melbourne', 'Brisbane']);
    const courseTypes = ref(['Technology', 'Education', 'Design']);
    const currentViewTab = ref('card');
    const loading = ref(true);
    const isJobFinder = ref(false);

    watch(
      () => props.jobs,
      (newJobs) => {
        isJobFinder.value = props.noSearchResult !== undefined || props.noJobsLeft !== undefined;
        loading.value = !newJobs?.data && !Array.isArray(newJobs);
      },
      { immediate: true, deep: true }
    );

    const jobsData = computed(() => {
      let jobsArray = [];
      if (isJobFinder.value) {
        jobsArray = Array.isArray(props.jobs?.data) ? props.jobs.data : [];
      } else {
        jobsArray = Array.isArray(props.jobs) ? props.jobs : props.jobs?.data || [];
      }
      return jobsArray
        .filter(job => job != null && typeof job === 'object')
        .map((job) => ({
          id: job.id || `temp-id-${Math.random().toString(36).substr(2, 9)}`,
          title: job.title || 'Untitled Job',
          company: job.company || 'Unknown Company',
          employment_type: job.employment_type || 'N/A',
          pay_amount: job.pay_amount || 0,
          pay_frequency: job.pay_frequency || 'N/A',
          expire_date: job.expire_date || new Date().toISOString(),
          created_at: job.created_at || new Date().toISOString(),
          location: job.location || 'Sydney',
        }));
    });

    const validJobs = computed(() => {
      if (!isJobFinder.value) return [];
      const jobsArray = Array.isArray(props.jobs?.data) ? props.jobs.data : Array.isArray(props.jobs) ? props.jobs : [];
      return jobsArray
        .filter(job => job != null && typeof job === 'object' && job.id && job.title)
        .map(job => ({
          id: job.id || `temp-id-${Math.random().toString(36).substr(2, 9)}`,
          title: job.title || 'Untitled Job',
          company: job.company.name || 'Unknown Company',
          location: job.location || 'Unknown Location',
          type: job.type || job.employment_type || 'N/A',
          pay_frequency: job.pay_frequency || 'N/A',
          posted: job.posted || job.created_at || new Date().toISOString(),
        }));
    });

    const noJobsMessage = computed(() => {
      if (isJobFinder.value) {
        if (props.noSearchResult) return 'No search results found';
        if (props.noJobsLeft) return 'No jobs left';
      }
      return 'No jobs available';
    });

    const total = computed(() => {
      if (isJobFinder.value) {
        return props.jobs?.meta?.total || 0;
      }
      return Array.isArray(props.jobs) ? props.jobs.length : props.jobs?.meta?.total || 0;
    });

    const lastPage = computed(() => {
      if (isJobFinder.value) {
        return props.jobs?.meta?.last_page || 1;
      }
      return Math.ceil(total.value / perPage.value) || 1;
    });

    const perPage = computed(() => {
      if (isJobFinder.value) {
        return props.jobs?.meta?.per_page || 6;
      }
      return 6;
    });

    const currentPage = computed(() => {
      if (isJobFinder.value) {
        return props.jobs?.meta?.current_page || 1;
      }
      return 1;
    });

    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = date.getTime() - now.getTime();
      const diffDays = Math.ceil(Math.abs(diffTime) / (1000 * 60 * 60 * 24));

      const text = diffTime < 0
        ? `Posted ${diffDays} day${diffDays === 1 ? '' : 's'} ago`
        : `Expiring in ${diffDays} day${diffDays === 1 ? '' : 's'}`;

      const showBadge = diffTime >= 0 && diffDays <= 7;

      return { text, showBadge };
    };

    const onPageChange = (page: number) => {
      if (page >= 1 && page <= lastPage.value) {
        emit('page-change', page);
      }
    };

   const onSearch = debounce(() => {
  emit('search', { searchQuery: searchQuery.value, filters: filters.value });
}, 300);

    const applyFilter = () => {
      emit('search', { searchQuery: searchQuery.value, filters: filters.value });
    };

    return {
      searchQuery,
      filters,
      locations,
      courseTypes,
      currentViewTab,
      formatDate,
      onSearch,
      applyFilter,
      jobsData,
      validJobs,
      loading,
      total,
      lastPage,
      perPage,
      currentPage,
      onPageChange,
      noJobsMessage,
      isJobFinder,
    };
  },
});
</script>

<style>
.custom-badge {
  color: #EF9A2A;
  background-color: #FEECDA;
}
</style>
