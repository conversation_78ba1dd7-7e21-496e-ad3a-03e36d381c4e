<?php

use App\Http\Controllers\Vue\CompanyController;
use App\Http\Controllers\Vue\ExploreIndustriesController;
use App\Http\Controllers\Vue\IndustryCategoryController;
use App\Http\Controllers\Vue\JobPostController;
use Illuminate\Support\Facades\Route;

/**
 * ===================================
 *  VUE API ROUTES
 * ===================================
 */
Route::group(['as' => 'api.'], function () {
    Route::group([ 'middleware' => 'auth'], function () {
        // Companies Route
        Route::group(['prefix' => 'companies', 'as' => 'companies.'], function () {
            Route::get('/{company}', [CompanyController::class, 'show'])->name('show');
            Route::post('/{company?}', [CompanyController::class, 'update'])->name('update');
            Route::get('/{company}/fetchCompanydata', [CompanyController::class, 'fetchCompany'])->name('fetchCompanydata');
            Route::get('/{company}/linked-content', [CompanyController::class, 'fetchLinkedContent'])
                ->name('linked-content');
            Route::get('/{company}/linked-courses', [CompanyController::class, 'fetchLinkedCourses'])
                ->name('linked-courses');
            Route::get('/{company}/jobs_opportunities', [CompanyController::class, 'fetchLinkedJobs'])->name('jobs_opportunities');
            Route::post('/{company}/toggle-fav', [CompanyController::class, 'toggleFav']);
            Route::get('/gameplans/latest', [CompanyController::class, 'latestGameplan']);
            Route::post('/add-to-gameplan/{gameplan}', [CompanyController::class, 'addToGameplan'])->name('add-to-gameplan');
        });

        Route::get('/gameplans/{gameplan}/companies', [CompanyController::class, 'fetchGameplanCompanies']);

        // Job Posts Route
        Route::apiResource('job-posts', JobPostController::class)->parameters(['job-posts' => 'jobPost']);
    
        // Industry Categories Route
        Route::group(['prefix' => 'industry-categories', 'as' => 'industry_categories.'], function () {
            Route::get('/', [IndustryCategoryController::class, 'index'])->name('index');
        });
        
    });
});
