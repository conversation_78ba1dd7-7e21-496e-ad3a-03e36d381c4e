const DocMenuConfig = [
    {
        pages: [
            {
                heading: "Home",
                route: "/dashboard",
                svgIcon: "media/icons/sidebar/home-solid.svg",
                fontIcon: "bi-app-indicator",
            },
            {
                heading: "Profile",
                route: "/profiles/edit",
                isExternal: true,
                svgIcon: "media/icons/sidebar/user-solid.svg",
                fontIcon: "bi-person",
            },
            {
                sectionTitle: "Accounts",
                route: "/accounts",
                isExternal: true,
                svgIcon: "media/icons/sidebar/user-friends-solid.svg",
                fontIcon: "bi-person",
                sub: [
                    {
                        heading: "Add Child +",
                        route: "/profiles/edit",
                        isExternal: true,
                        showPopup: true
                    }
                ]
            },
            {
                sectionTitle: "Explore",
                route: "/explore",
                svgIcon: "media/icons/sidebar/explore.svg",
                fontIcon: "bi-people-fill",
                sub: [
                    {
                        heading: "Industries",
                        route: "/exploreindustries",
                        isExternal: true,
                    },
                    {
                        heading: "e-Magazine",
                        route: "/e-magazines/editions",
                        isExternal: true,
                    },
                    {
                        title: 'Career Profiling',
                        route: 'career/profiling',
                        isExternal: true,
                    },
                    // {
                    //     title: 'Video Profiling',
                    //     route: 'video/profiling',
                    //     isExternal: true,
                    // },
                ]
            },
            {
                sectionTitle: "Tasks",
                route: "/tasks",
                svgIcon: "media/icons/sidebar/laptop-solid.svg",
                fontIcon: "bi-stack",
                sub: [
                    {
                        heading: "Lessons",
                        route: "/tasks",
                        isExternal: true,
                    },
                    {
                        heading: "Virtual Work Experience",
                        route: "/exploreworkexperience",
                        isExternal: true,
                    },
                    {
                        heading: "Skills Training",
                        route: "/wew/skillstraining",
                        isExternal: true,
                    },
                ],
            },
            {
                sectionTitle: "Tools",
                route: "/tools",
                svgIcon: "media/icons/sidebar/toolbox-solid.svg",
                fontIcon: "bi-bar-chart",
                sub: [
                    {
                        heading: "Job Finder",
                        // route: "https://au.indeed.com/",
                        route: "/#/tools/jobfinder",
                        isExternal: true,
                        inNewTab: true
                    },
                    {
                        heading: "Scholarship Finder",
                        route: "/tools/scholarshipsfinder",
                        // isExternal: true,
                    },
                    {
                        heading: "Resume Builder",
                        route: "/cvs",
                        isExternal: true,
                    },
                    {
                        heading: "Course Finder",
                        route: "/tools/coursefinder",
                        // isExternal:true,
                    },
                    {
                        heading: "ePortfolio",
                        route: "/eportfolio",
                        isExternal: true,
                    },
                    {
                        heading: "Subject Selections",
                        route: "/subjects-selection",
                        isExternal: true,
                    },
                ]
            },
            {
                sectionTitle: "Support",
                route: "/support",
                svgIcon: "media/icons/sidebar/support.svg",
                fontIcon: "bi-node-minus",
                sub: [
                    // {
                    //     heading: "Noticeboard",
                    //     route: "/noticeboard",
                    //     isExternal: true,
                    // },
                    // {
                    //     heading: "Forum",
                    //     route: "https://help.thecareersdepartment.com/en//community/topics",
                    //     isExternal: true,
                    // },
                    {
                        heading: "Help Centre",
                        route: "https://help.thecareersdepartment.com/en/ ",
                        isExternal: true,
                        inNewTab: true,
                    }
                ]
            },
        ],
    },


];

export default DocMenuConfig;
