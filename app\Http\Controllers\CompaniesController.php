<?php

namespace App\Http\Controllers;

use App\Role;
use App\State;
use App\Company;
use App\Country;
use App\SchoolDetail;
use App\IndustryCategory;
use App\Industryunit;
use App\WorkexperienceTemplate;
use App\SkillstrainingTemplate;
use App\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CompaniesController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = Company::query();

        // Apply filters
        if ($company = $request->company) {
            $query->whereHas('detail', function($q) use ($company) {
                $q->where('name', 'like', "%{$company}%");
            });
        }

        if ($state = $request->state) {
            $query->whereHas('states', function($q) use ($state) {
                $q->where('states.id', $state);
            });
        }

        $companies = $query->paginate(15);
        $states = Country::find(1)->states;

        return view('companies.index', compact('companies', 'states'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $states = State::all();
        $countries = Country::All();
        $industries = IndustryCategory::all();
        $industryunits = Industryunit::all();
        $workexperienceTemplates = WorkexperienceTemplate::all();
        $skillstrainingTemplates = SkillstrainingTemplate::all();
        $lessons = Lesson::all();

        return view('companies.create', compact('countries', 'states', 'industries', 'industryunits', 'workexperienceTemplates', 'skillstrainingTemplates', 'lessons'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $company = new Company();

        $role_id = Role::where('name', 'Company')->value('id');
        $company->fill([
            'role_id' => $role_id,
            'name' => $request->company_name,
            'email' => Str::random(20),
            'password' => bcrypt(Str::random(10)),
        ]);
        $company->save();

        $detail = new SchoolDetail();

        $logo = null;

        if ($request->hasFile('logo') && $request->file('logo')->isValid()) {
            $logo = $request->file('logo')->store('attachments/companies', ['visibility' => 'public']);
        }

        $detail->fill([
            'name' => $request->company_name,
            'logo' => $logo,
            'institute_type' => 'company',
            'contact_person' => $request->contact_person,
            'position' => $request->position,
            'email' => $request->email,
            'phone' => $request->phone,
            'subscription_start_date' => $request->subscription_start_date ? date('Y-m-d', strtotime($request->subscription_start_date)) : null,
            'subscription_ending_on' => $request->subscription_ending_on ? date('Y-m-d', strtotime($request->subscription_ending_on)) : null,
            'status' => $request->status,
        ]);

        $company->detail()->save($detail);

        // Save relationships
        if ($request->has('states')) {
            $company->states()->sync($request->states);
        }

        if ($request->has('industries')) {
            $company->industries()->sync($request->industries);
        }

        if ($request->has('industryunits')) {
            $company->industryunits()->sync($request->industryunits);
        }

        if ($request->has('workexperience_templates')) {
            $company->workexperienceTemplates()->sync($request->workexperience_templates);
        }

        if ($request->has('skillstraining_templates')) {
            $company->skillstrainingTemplates()->sync($request->skillstraining_templates);
        }

        if ($request->has('lessons')) {
            $company->lessons()->sync($request->lessons);
        }

        return redirect()->route('companies.index')->with('message', 'Company created successfully!');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Company  $company
     * @return \Illuminate\Http\Response
     */
    public function show(Company $company)
    {
        return view('companies.show', compact('company'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Company  $company
     * @return \Illuminate\Http\Response
     */
    public function edit(Company $company)
    {
        $states = State::all();
        $countries = Country::All();
        $industries = IndustryCategory::all();
        $industryunits = Industryunit::all();
        $workexperienceTemplates = WorkexperienceTemplate::all();
        $skillstrainingTemplates = SkillstrainingTemplate::all();
        $lessons = Lesson::all();

        return view('companies.edit', compact('company', 'countries', 'states', 'industries', 'industryunits', 'workexperienceTemplates', 'skillstrainingTemplates', 'lessons'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Company  $company
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Company $company)
    {
        $company->update([
            'name' => $request->company_name,
        ]);

        $detail = $company->detail;

        if (!$detail) {
            $detail = new SchoolDetail();
            $detail->school_id = $company->id;
            $logo = null;
        } else {
            if ($request->current_logo == "" && !empty($detail->logo) && Storage::exists($detail->logo)) {
                Storage::delete($detail->logo);
                $logo = null;
            } else {
                $logo = $detail->logo;
            }
        }

        if ($request->hasFile('logo') && $request->file('logo')->isValid()) {
            if (!empty($detail->logo) && Storage::exists($detail->logo)) {
                Storage::delete($detail->logo);
            }
            $logo = $request->file('logo')->store('attachments/companies', ['visibility' => 'public']);
        }

        $data = [
            'name' => $request->company_name,
            'logo' => $logo,
            'contact_person' => $request->contact_person,
            'position' => $request->position,
            'email' => $request->email,
            'phone' => $request->phone,
            'subscription_start_date' => $request->subscription_start_date,
            'subscription_ending_on' => $request->subscription_ending_on,
            'status' => $request->status,
        ];

        if ($detail->exists) {
            $detail->update($data);
        } else {
            $detail->fill($data);
            $company->detail()->save($detail);
        }

            $company->states()->sync($request->states);
            $company->industries()->sync($request->industries);
            $company->industryunits()->sync($request->industryunits);
            $company->workexperienceTemplates()->sync($request->workexperience_templates);
            $company->skillstrainingTemplates()->sync($request->skillstraining_templates);
            $company->lessons()->sync($request->lessons);

        return redirect()->route('companies.index')->with('message', 'Company updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Company  $company
     * @return \Illuminate\Http\Response
     */
    public function destroy(Company $company)
    {
        $company->delete();

        if (request()->wantsJson()) {
            return response([], 204);
        }

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Company has been deleted successfully!');
    }
}
