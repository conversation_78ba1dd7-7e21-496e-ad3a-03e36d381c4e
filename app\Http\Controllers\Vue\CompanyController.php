<?php

namespace App\Http\Controllers\Vue;

use App\Company;
use App\Gameplan;
use App\GameplanCompany;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\UpdateCompanyRequest;
use App\Http\Resources\CompanyResource;
use App\JobPost;
use App\Lesson;
use App\Services\CompanyService;
use App\Services\TimelineService;
use App\SkillstrainingTemplate;
use App\WorkexperienceTemplate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class CompanyController extends Controller
{
    public function __construct(
        protected CompanyService $companyService
    )
    {
        
    }

    /**
     * Get specific company
     *
     * @param \App\Company $company
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Company $company): JsonResponse
    {
        $company->load([
            'industries:id,name',
        ]);

        return response()->json([
            'success' => true,
            'data' => new CompanyResource($company)
        ]);
    }

    /**
     * Update Company and its details
     *
     * @param \App\Http\Requests\Api\UpdateCompanyRequest $request
     * @param \App\Company|null $company
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateCompanyRequest $request, ?Company $company): JsonResponse
    {
        $data = $request->validated();
        $user = Auth::user();
        $company = $company?->exists ? $company : $user->company;

        if (!$company) {
            return response()->json([
                'success' => false,
                'message' => 'Company not found'
            ], 404);
        }
        
        $detail = $company->detail;

        if (!$detail) {
            return response()->json([
                'success' => false,
                'message' => 'Company Details not found'
            ], 404);
        }

        $res = $this->companyService->update($company, $data, true, ['industries:id,name']);

        return response()->json($res);
    }

    public function fetchCompany(Company $company): JsonResponse
    {
        $company->load([
            'industries:id,name',
            'states:id,name',
            'pages'
        ]);

        return response()->json([
            'success' => true,
            'data' => new CompanyResource($company)
        ]);
    }

    public function fetchLinkedContent($companyId, Request $request)
    {
        $company = Company::findOrFail($companyId);
        $perPage = $request->per_page ?? 6;
        $search = request()->query('search', '');

        $query = $company->industryunits()->orderByDesc('created_at')->distinct();

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'LIKE', "%{$search}%")
                    ->orWhere('type', 'LIKE', "%{$search}%");
            });
        }

        $paginator = $query->paginate($perPage);

        return response()->json([
            'data' => $paginator->items(),
            'meta' => [
                'total' => $paginator->total(),
                'current_page' => $paginator->currentPage(),
                'per_page' => $paginator->perPage(),
                'last_page' => $paginator->lastPage(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
            ],
        ]);
    }

    public function fetchLinkedCourses($companyId, Request $request)
    {
        $company = Company::findOrFail($companyId);

        $search = $request->search;
        $perPage = $request->per_page ?? 4;

        $lessonQuery = DB::table('lessons')
            ->join('company_lesson', 'lessons.id', '=', 'company_lesson.lesson_id')
            ->leftJoin('lessonresponses', function ($join) {
                $join->on('lessons.id', '=', 'lessonresponses.lesson_id')
                    ->where('lessonresponses.student_id', Auth::id());
            })
            ->leftJoin('lessonsteps', 'lessons.id', '=', 'lessonsteps.lesson_id')
            ->where('company_lesson.company_id', $company->id)
            ->select(
                'lessons.id as module_id',
                'lessons.title',
                'lessons.tileimage',
                'lessons.level',
                DB::raw("CASE 
                WHEN lessonresponses.status = 'Submitted' THEN 'completed'
                WHEN lessonresponses.status = 'Draft' THEN 'in progress'
                ELSE 'not started'
                END as status"),
                DB::raw("CASE WHEN lessons.tileimage IS NOT NULL 
                THEN CONCAT('" . Storage::url('') . "', lessons.tileimage) 
                ELSE NULL END as tileimage_fullpath"),
                DB::raw("'Lessons' as module_type")
            )
            ->groupBy('lessons.id', 'lessons.title', 'lessons.tileimage', 'lessons.level');


        $vweQuery = DB::table('workexperience_templates')
            ->join('company_workexperience_template', 'workexperience_templates.id', '=', 'company_workexperience_template.workexperience_template_id')
            ->leftJoin('workexperience_responses', function ($join) {
                $join->on('workexperience_templates.id', '=', 'workexperience_responses.template_id')
                    ->where('workexperience_responses.student_id', Auth::id());
            })
            ->leftJoin('steps', 'workexperience_templates.id', '=', 'steps.template_id')
            ->where('company_workexperience_template.company_id', $company->id)
            ->select(
                'workexperience_templates.id as module_id',
                'workexperience_templates.title',
                'workexperience_templates.tileimage',
                'workexperience_templates.level',
                DB::raw("CASE 
                WHEN workexperience_responses.status = 'Submitted' THEN 'completed'
                WHEN workexperience_responses.status = 'Draft' THEN 'in progress'
                ELSE 'not started'
                END as status"),
                DB::raw("CASE WHEN workexperience_templates.tileimage IS NOT NULL 
                THEN CONCAT('" . Storage::url('') . "', workexperience_templates.tileimage) 
                ELSE NULL END as tileimage_fullpath"),
                DB::raw("'Virtual Work Experience' as module_type")
            )
            ->groupBy('workexperience_templates.id', 'workexperience_templates.title', 'workexperience_templates.tileimage', 'workexperience_templates.level');


        $stQuery = DB::table('skillstraining_templates')
            ->join('company_skillstraining_template', 'skillstraining_templates.id', '=', 'company_skillstraining_template.skillstraining_template_id')
            ->leftJoin('skillstraining_responses', function ($join) {
                $join->on('skillstraining_templates.id', '=', 'skillstraining_responses.template_id')
                    ->where('skillstraining_responses.student_id', Auth::id());
            })
            ->leftJoin('wew_steps', 'skillstraining_templates.id', '=', 'wew_steps.stepable_id')
            ->where('company_skillstraining_template.company_id', $company->id)
            ->select(
                'skillstraining_templates.id as module_id',
                'skillstraining_templates.title',
                'skillstraining_templates.tileimage',
                'skillstraining_templates.level',
                DB::raw("CASE 
                WHEN skillstraining_responses.status = 'Submitted' THEN 'completed'
                WHEN skillstraining_responses.status = 'Draft' THEN 'in progress'
                ELSE 'not started'
                END as status"),
                DB::raw("CASE WHEN skillstraining_templates.tileimage IS NOT NULL 
                THEN CONCAT('" . Storage::url('') . "', skillstraining_templates.tileimage) 
                ELSE NULL END as tileimage_fullpath"),
                DB::raw("'Skills Training' as module_type")
            )
            ->groupBy('skillstraining_templates.id', 'skillstraining_templates.title', 'skillstraining_templates.tileimage', 'skillstraining_templates.level');


        $union = $lessonQuery->union($vweQuery)->union($stQuery);

        $filteredQuery = DB::query()
            ->fromSub($union, 'sub')
            ->when($search, function ($q) use ($search) {
                $q->where(function ($inner) use ($search) {
                    $inner->where('title', 'LIKE', "%{$search}%")
                        ->orWhere('level', 'LIKE', "%{$search}%");
                });
            });

        $paginator = $filteredQuery->paginate($perPage);

        return response()->json([
            'data' => $paginator->items(),
            'meta' => [
                'total' => $paginator->total(),
                'current_page' => $paginator->currentPage(),
                'per_page' => $paginator->perPage(),
                'last_page' => $paginator->lastPage(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
            ],
        ]);
    }

    public function fetchLinkedJobs($companyId = null, Request $request)
    {
        if ($companyId === 'undefined' || $companyId === 'null') {
            $companyId = null;
        }

        $perPage = $request->per_page ?? 6; // Default per_page to 10

        // Base query
        $query = JobPost::with('company:id,name')
        ->select(
            'id',
            'company_id',
            'title',
            'employment_type',
            'pay_frequency',
            'expire_date',
            'pay_amount',
            'created_at'
        );

        // Filter by company if provided
        if (!empty($companyId)) {
            $query->where('company_id', $companyId);
        }

        if ($request->has('searchQuery') && !empty($request->input('searchQuery'))) {
            $query->where('title', 'like', '%' . $request->input('searchQuery') . '%');
        }

        // Filter by job type
        if ($request->filled('jobtype')) {
            $jobTypes = (array) $request->jobtype; // Ensure array
            $query->whereIn('employment_type', $jobTypes);
        }

    if ($request->has('employment_type') && !empty($request->input('employment_type'))) {
        $query->where('employment_type', 'like', '%' . $request->input('employment_type') . '%');
        
    }
    if ($request->has('pay_frequency') && !empty($request->input('pay_frequency'))) {
        $query->where('pay_frequency', 'like', '%' . $request->input('pay_frequency') . '%');
        
    }

        // Paginate
        $paginator = $query->paginate($perPage);

        return response()->json([
            'data' => $paginator->items(),
            'meta' => [
                'total' => $paginator->total(),
                'current_page' => $paginator->currentPage(),
                'per_page' => $paginator->perPage(),
                'last_page' => $paginator->lastPage(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
            ],
        ]);
    }


      public function toggleFav($companyId)
    {
        $user = Auth::user();
        $company = Company::findOrFail($companyId);

        $user->toggleFavorite($company);

        $isFav = $user->hasFavorited($company);

        return response()->json([
            'success' => true,
            'isFav' => $isFav,
            'message' => $isFav ? 'Company added to favorites.' : 'Company removed from favorites.'
        ]);
    }

    public function latestGameplan()
    {
        $userId = Auth::id();

        $gameplan = Gameplan::where('user_id', $userId)
            ->latest('id')  
            ->first();

        if (!$gameplan) {
            return response()->json(['message' => 'No gameplan found'], 404);
        }

        return response()->json($gameplan);
    }

    public function fetchGameplanCompanies($gameplanId)
    {
        $userId = Auth::id();

        $gameplan = Gameplan::where('user_id', $userId)
            ->where('id', $gameplanId)
            ->first();

        if (!$gameplan) {
            return response()->json(['message' => 'No gameplan found'], 404);
        }

        $companies = DB::table('gameplan_companies')
            ->where('gameplan_id', $gameplanId)
            ->get();

        // dd($companies);

        return response()->json($companies);
    }

    public function addToGameplan(Request $request, $gameplanId)
    {
        $request->validate([
            'company_id' => 'required|exists:users,id',
        ]);

        $user = Auth::user();
        $companyId = $request->input('company_id');
        $companyName = $request->input('company_name');

        // Fetch the game plan
        $gameplan = $user->lastPlan()->where('id', $gameplanId)->first();
        if (!$gameplan) {
            return response()->json([
                'success' => false,
                'message' => 'Gameplan not found or does not belong to the user.'
            ], 404);
        }

        // Fetch the company
        $company = Company::find($companyId);
        if (!$company) {
            return response()->json([
                'success' => false,
                'message' => 'Company not found.'
            ], 404);
        }

        // Check for existing link
        $exists = $gameplan->companies()
            ->where('gameplan_companies.company_id', $companyId)
            ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'Company is already linked to this gameplan.'
            ], 400);
        }

        // Create the GameplanCompany entry
        $gameplanCompany = GameplanCompany::create([
            'gameplan_id'  => $gameplan->id,
            'company_id'   => $companyId,
            'company_name' => $companyName ?? $company->name,
        ]);

        // Log directly against GameplanCompany
        $timelineService = app(TimelineService::class);
        $timelineService->log($gameplanCompany, 'created');

        return response()->json([
            'success' => true,
            'message' => 'Company successfully added to the gameplan.'
        ]);
    }

 

}
