<?php

namespace App\Services;

use App\Company;
use App\Http\Resources\CompanyResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class CompanyService {
    /**
     * Update Company and its related data
     *
     * @param \App\Company $company
     * @param array $data
     * @param boolean $wantsResource
     * @param array $with Relations wants in return
     * @return void
     */
    public function update(Company $company, array $data, $wantsResource = false, $with = [])
    {
        $detail = $company->detail;

        // Company Update
        $company->update(Arr::only($data, ['name']));

        // Sync Relations
        $this->syncRelatedData($company, 'industries', $data);

        if ($detail) {
            $updateData = Arr::only($data, ['name', 'description', 'status', 'url', 'social_links']);

            $updateData['logo']   = $this->handleFileUpload($data['logo'] ?? null, $detail->logo ?? null);
            $updateData['banner'] = $this->handleFileUpload($data['banner'] ?? null, $detail->banner ?? null);

            if (($data['status'] ?? null) === 'published') {
                $updateData['last_published_at'] = now();
            }

            $detail->update($updateData);
        }

        $company = $company->refresh();

        if (!empty($with)) {
            $company->load($with);
        }

        return [
            'success' => true,
            'message' => 'Company data updated successfully',
            'data' => $wantsResource ? (new CompanyResource($company)) : $company
        ];
    }

    /**
     * Handle file upload and old file cleanup.
     */
    protected function handleFileUpload($file, ?string $oldPath): ?string
    {
        if ($file instanceof \Illuminate\Http\UploadedFile && $file->isValid()) {
            if ($oldPath && Storage::exists($oldPath)) {
                Storage::delete($oldPath);
            }

            return $file->store('attachments/companies', ['visibility' => 'public']);
        }

        if ($file === null && $oldPath) {
            if (Storage::exists($oldPath)) {
                Storage::delete($oldPath);
            }

            return null;
        }
        
        return $oldPath; // keep old file if no new one uploaded
    }

    public function syncRelatedData(Company $company, array|string $relationNames, array $data)
    {
        $syncCallback = function($company, $relationName, $data) {
            if (array_key_exists($relationName, $data) && method_exists(Company::class, $relationName)) {
                $data[$relationName] = (!empty($data[$relationName]) ? $data[$relationName] : []);

                $company->{$relationName}()->sync((array) $data[$relationName]);
            }
        };
        
        if (is_array($relationNames)) {
            foreach ($relationNames as $relationName) {
                $syncCallback($company, $relationName, $data);
            }
        } else {
            $syncCallback($company, $relationNames, $data);
        }
    }
}