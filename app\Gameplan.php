<?php

namespace App;

use App\Services\TimelineService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class  Gameplan extends Model
{
    protected $fillable = [
        'user_id',
        'title',
        'finishing_school',
        'anything_else',
    ];

    protected $appends = ['all_courses'];

    protected static function booted()
    {
        $clearIndustriesChartCache = function ($gameplan) {
            // Clear industries chart cache for all companies when gameplan changes
            // since this affects the "latest gameplan" logic
            \Cache::forget('industries_chart_data_*');

            // Clear specific company caches if we can determine which companies are affected
            if ($gameplan->industries && $gameplan->industries->isNotEmpty()) {
                $industryIds = $gameplan->industries->pluck('id');
                $companyIds = \DB::table('company_industry_category')
                    ->whereIn('industry_category_id', $industryIds)
                    ->pluck('company_id');

                foreach ($companyIds as $companyId) {
                    \Cache::forget("industries_chart_data_{$companyId}");
                }
            }
        };

        static::created(function ($gameplan) use ($clearIndustriesChartCache) {
            (new TimelineService)->log(
                $gameplan, // model
                'created', // event
                'created', // description
            );

            // Clear industries chart cache
            $clearIndustriesChartCache($gameplan);
        });

        static::updated($clearIndustriesChartCache);
        static::deleted($clearIndustriesChartCache);
    }


    /**
     * Relationship with the User who created the gameplan.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function finishingSchool()
    {
        return $this->belongsTo(GameplanQuestionOption::class, 'finishing_school');
    }

    /**
     * Many-to-Many relationship with IndustryCategory through the pivot table `gameplan_industries`.
     */
    public function industries()
    {
        return $this->belongsToMany(
            IndustryCategory::class,
            'gameplan_industries', // Pivot table
            'gameplan_id',         // Foreign key on pivot table referencing Gameplan
            'industry_category_id' // Foreign key on pivot table referencing IndustryCategory
        )->withPivot('priority') // Include `priority` field from the pivot table
            ->withTimestamps()    // Include pivot table timestamps
            ->orderBy('gameplan_industries.priority');
    }

    /**
     * Many-to-Many relationship with Course through the renamed pivot table `course_gameplan`.
     */
    public function courses()
    {
        return $this->belongsToMany(Course::class)->withTimestamps();    // Include pivot table timestamps
    }

    public function otherCourses()
    {
        return $this->hasMany(CourseGameplan::class)->whereNull('course_id')->whereNotNull('other_course')
            ->select('other_course', 'created_at', 'updated_at');
    }

    protected function allCourses(): Attribute
    {
        return Attribute::make(
            get: function () {
                $courses = $this->courses;
                $customCourses = $this->otherCourses;
                return $courses->merge($customCourses);
            }
        );
    }

    /**
     * Many-to-Many relationship with GameplanQuestionOption through the pivot table `gameplan_interested_in`.
     */
    public function interestedIn()
    {
        return $this->belongsToMany(
            GameplanQuestionOption::class,
            'gameplan_interested_in',  // Pivot table
            'gameplan_id',             // Foreign key on pivot table referencing Gameplan
            'gameplan_question_option_id' // Foreign key on pivot table referencing GameplanQuestionOption
        )->withTimestamps();          // Include pivot table timestamps
    }

    /**
     * One-to-Many relationship with jobs for free-text input.
     */
    public function jobs()
    {
        return $this->hasMany(GameplanJob::class);
    }

    /**
     * Polymorphic One-to-Many relationship with institutes.
     */
    public function institutes()
    {
        return $this->hasMany(GameplanInstitute::class);
    }

    /**
     * One-to-Many relationship with companies for free-text input.
     */
    // public function companies()
    // {
    //     return $this->hasMany(GameplanCompany::class);
    // }


   public function companies()
    {
        return $this->belongsToMany(Company::class, 'gameplan_companies')
                    ->withPivot('company_name');
    }

    public function companyEntries()
    {
        return $this->hasMany(GameplanCompany::class);
    }

    public function allCompanies()
    {
        return $this->companyEntries()->with('company');
    }

    public function questionAnswers()
    {
        return $this->hasMany(GameplanQuestionAnswer::class);
    }
}
