<template>
    <CardsSkeleton :is-loading="isInitiallyLoading" />
    <div v-if="!isInitiallyLoading">
        <router-link :to="{ name: 'company', query: { tab: 'JobsandOpportunities' } }" class="btn btn-link">
            <i class="bi bi-arrow-left fs-5 text-black"></i>
            <span class="fs-6 fw-bold">Back to View All</span>
        </router-link>

        <form
            @submit.prevent="jobPostFormSave()"
            ref="jobPostFormRef"
            @keydown.enter.prevent="jobPostFormSave()"
        >
            <div class="tophead mt-10 d-flex align-items-center justify-content-between">
                <h2>
                    Job or Opportunity
                </h2>

                <div style="display: flex; gap: 10px;">
                    <!-- Save Button -->
                    <button
                        type="button"
                        class="btn btn-secondary px-18 py-4"
                        data-kt-menu-trigger="click"
                        data-kt-menu-placement="bottom-end"
                        :disabled="isLoading"
                        @click="jobPostFormSave()"
                    >
                        Save
                    </button>
                    <button
                        type="button"
                        class="btn btn-secondary px-18 py-4"
                        data-kt-menu-trigger="click"
                        data-kt-menu-placement="bottom-end"
                        :disabled="isLoading"
                        @click="jobPostFormSaveAndClose()"
                    >
                        Save & Close
                    </button>
                    <div class="d-flex align-items-center">
                        <span class="spinner-border spinner-border-sm" v-if="isLoading" aria-hidden="true"></span>
                    </div>
                </div>
            </div>

            <!-- Row -->
            <div class="row mt-5">
                <!-- Left Column -->
                <div class="col-lg-9">
                    <!-- Details Section -->
                    <div class="card mb-4 border">
                        <div class="card-header">
                            <p class="card-title fs-3 mt-5">Details</p>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="row">
                                    <!-- Location -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Location </label>
                                        <Field v-slot="{ field }" name="locations">
                                            <CustomSelect
                                                v-bind="field"
                                                :options="[
                                                    { value: 'nsw', label: 'New South Wales' },
                                                ]"
                                                :can-clear="false"
                                                mode="single"
                                                placeholder="Select locations"
                                            />
                                        </Field>

                                        <ErrorMessage name="locations" as="div" class="text-danger mt-1" />
                                    </div>

                                    <!-- Work Mode -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Work Mode <span class="text-danger">*</span></label>
                                        <Field v-slot="{ field }" name="work_mode">
                                            <CustomSelect
                                                v-bind="field"
                                                :options="[
                                                    { value:  'online', label: 'Online'},
                                                    { value:  'remote', label: 'Remote'},
                                                    { value:  'in person', label: 'In Person'},
                                                    { value:  'hybrid', label: 'Hybrid'},
                                                ]"
                                                :can-clear="false"
                                                :required="true"
                                                mode="single"
                                                placeholder="Select work mode"
                                            />
                                        </Field>

                                        <ErrorMessage name="work_mode" as="div" class="text-danger mt-1" />
                                    </div>

                                    <!-- Duration -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Duration</label>
                                        
                                        <Field
                                            as="input"
                                            type="text"
                                            name="duration"
                                            placeholder="Enter Duration here"
                                            class="form-control mb-2"
                                        />

                                        <ErrorMessage name="duration" as="div" class="text-danger mt-1" />
                                    </div>

                                    <!-- Employment Type -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Employment Type <span class="text-danger">*</span></label>
                                        <Field v-slot="{ field }" name="employment_type">
                                            <CustomSelect
                                                v-bind="field"
                                                :options="[
                                                    { value:  'part-time', label: 'Part-Time'},
                                                    { value:  'full-time', label: 'Full-Time'},
                                                    { value:  'internship', label: 'Internship'},
                                                    { value:  'casual', label: 'Casual'},
                                                    { value:  'contract', label: 'Contract'},
                                                    { value:  'temporary', label: 'Temporary'},
                                                ]"
                                                :can-clear="false"
                                                :required="true"
                                                mode="single"
                                                placeholder="Select employment type"
                                            />
                                        </Field>

                                        <ErrorMessage name="employment_type" as="div" class="text-danger mt-1" />
                                    </div>

                                    <!-- Seniority Level -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Seniority Level <span class="text-danger">*</span></label>
                                        <Field v-slot="{ field }" name="seniority_level">
                                            <CustomSelect
                                                v-bind="field"
                                                :options="[
                                                    { value: 'intern', label: 'Intern'},
                                                    { value: 'graduate', label: 'Graduate'},
                                                    { value: 'junior', label: 'Junior'},
                                                    { value: 'mid level', label: 'Mid Level'},
                                                    { value: 'senior', label: 'Senior'},
                                                    { value: 'director', label: 'Director'},
                                                    { value: 'executive', label: 'Executive'},
                                                ]"
                                                :can-clear="false"
                                                :required="true"
                                                mode="single"
                                                placeholder="Select seniority level"
                                            />
                                        </Field>

                                        <ErrorMessage name="seniority_level" as="div" class="text-danger mt-1" />
                                    </div>

                                    <!-- Expiring On -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Expiring on <span class="text-danger">*</span></label>
                                        <Field
                                            name="expire_date"
                                            as="input"
                                            type="date"
                                            class="form-control"
                                        />
                                        <ErrorMessage name="expire_date" as="div" class="text-danger mt-1" />
                                    </div>

                                    <!-- Paid -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Paid <span class="text-danger">*</span></label>
                                        <Field v-slot="{ field }" name="is_paid">
                                            <CustomSelect
                                                v-bind="field"
                                                :options="[
                                                    { value:  1, label: 'Yes'},
                                                    { value:  0, label: 'No'},
                                                ]"
                                                :can-clear="false"
                                                :required="true"
                                                mode="single"
                                                placeholder="Select if paid or not"
                                            />
                                        </Field>

                                        <ErrorMessage name="is_paid" as="div" class="text-danger mt-1" />
                                    </div>

                                    <div class="col-md-6 mb-3" v-if="values.is_paid === 1">
                                        <label class="form-label">Pay Amount <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <Field
                                                name="pay_amount"
                                                as="input"
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                class="form-control w-25"
                                                placeholder="Enter amount"
                                            />
                                            <Field as="select" name="pay_frequency" class="form-select">
                                                <option value="">Select frequency</option>
                                                <option
                                                    v-for="opt in [
                                                        { value: 'weekly', label: 'Weekly' },
                                                        { value: 'monthly', label: 'Monthly' },
                                                        { value: 'yearly', label: 'Yearly' },
                                                    ]"
                                                    :key="opt.value"
                                                    :value="opt.value"
                                                >
                                                    {{ opt.label }}
                                                </option>
                                            </Field>
                                        </div>
                                        <ErrorMessage name="pay_amount" as="div" class="text-danger mt-1" />
                                        <ErrorMessage name="pay_frequency" as="div" class="text-danger mt-1" />
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>


                    <div class="card mb-4 border">
                        <div class="card-header ">
                            <p class=" position-relative fs-3 mt-5">Description</p>
                        </div>
                        <div class="card-body">
                            <form>
                                <!-- Row -->
                                <div class="row">
                                    <div class="col-md-12 row d-flex flex-column">
                                        <!-- Job Title -->
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Job Title <span class="text-danger">*</span></label>
                                            <Field
                                                class="form-control"
                                                name="title"
                                                as="input"
                                                type="text"
                                                placeholder="Enter job title here"
                                            />
                                            
                                            <ErrorMessage name="title" as="div" class="text-danger mt-1" />
                                        </div>
                                        <!-- Job Title ./ -->
                                    </div>

                                    <div class="col-md-12">
                                        <label class="form-label">Description</label>
                                        <Field v-slot="{ field }" name="description">
                                            <froala
                                                :tag="'textarea'"
                                                v-bind="field"
                                                :config="froalaConfig"
                                            ></froala>
                                        </Field>
                                        <ErrorMessage name="description" as="div" class="text-danger mt-1" />
                                    </div>
                                </div>
                                <!-- Row ./ -->
                            </form>
                        </div>

                    </div>
                </div>
                <!-- Left Column ./ -->

                <!-- Right Column -->
                <div class="col-lg-3">
                    <!-- Status Card -->
                    <div class="card mb-8 border">
                        <div>
                            <div class="card card-flush">
                                <div class="card-header">
                                    <p class="card-title">Status</p>

                                    <div class="card-toolbar">
                                        <div class="rounded-circle  w-15px h-15px" :class="jobPost?.status === 'published' ? 'bg-success' : 'bg-warning'" id="kt_ecommerce_add_category_status">
                                        </div>
                                    </div>
                                </div>

                                <div class="card-body pt-0">
                                    <Field v-slot="{ field }" name="status">
                                        <CustomSelect
                                            v-bind="field"
                                            :options="[
                                                { value: 'draft', label: 'Draft' },
                                                { value: 'published', label: 'Published' },
                                            ]"
                                            :can-clear="false"
                                            :required="true"
                                            mode="single"
                                            placeholder="Select status"
                                        />
                                    </Field>

                                    <ErrorMessage name="status" as="div" class="text-danger mt-1" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Status Card ./ -->

                    <!-- Banner Image Card -->
                    <div class="card card-flush mb-8 border">
                        <div class="card-header">
                            <p class="card-title">Banner image</p>
                        </div>

                        <div class="card-body text-center pt-0">
                            <ImageUploader
                                name="banner"
                                help-text="Set the banner image. Only *.png, *.jpg and *.jpeg image files are accepted"
                                accept=".png, .jpg, .jpeg"
                            />
                        </div>
                    </div>
                    <!-- Banner Image Card ./ -->

                    <!-- Apply Now Card -->
                    <div class="card card-flush border mb-8 ">
                        <div class="card-header">
                            <p class="card-title">Apply Now Link</p>
                        </div>

                        <div class="card-body">
                            <label class="form-label">Insert Application Page URL</label>
                            <Field
                                class="form-control"
                                name="apply_now_link"
                                type="text"
                                placeholder="https://www.example.com"
                            />
                            <ErrorMessage name="apply_now_link" as="div" class="text-danger mt-1" />
                        </div>
                    </div>
                    <!-- Apply Now Card ./ -->

                    <!-- Additional Resources Card -->
                    <div class="card card-flush border">
                        <div class="card-header">
                            <p class="card-title">Additional Resources</p>
                        </div>

                        <div class="card-body">
                            <LinksRepeater name="additional_links" add-more-label="Link" />
                        </div>
                    </div>
                    <!-- Additional Resources Card ./ -->

                </div>
                <!-- Right Column ./ -->

            </div>
            <!-- Row ./ -->
        </form>
    </div>
</template>


<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from "vue";
import { ErrorMessage, Field, useForm, configure } from "vee-validate";
import ApiService from "@/core/services/ApiService";
import { useStore } from 'vuex';
import { normalizeBackendErrors, showErrorAlert, showSuccessAlert } from "@/utils/helpers";
import * as yup from 'yup';
import CustomSelect from "@/components/common/CustomSelect.vue";
import ImageUploader from "@/components/common/ImageUploader.vue";
import LinksRepeater from "@/components/common/LinksRepeater.vue";
import { useRoute, useRouter } from "vue-router";
import CardsSkeleton from "@/components/common/CardsSkeleton.vue";


// -----------------------------
// Core
// -----------------------------
const isLoading = ref(false);
const isInitiallyLoading = ref(false);

// FROALA EDITOR 
const froalaConfig = {
    key: "hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",
    height: 300,
    attribution: false,
};

const store = useStore();
const currentUser = store.getters.currentUser;
const route = useRoute();
const router = useRouter();
const jobPostId = (route.params.id as string | undefined) ?? null

// -----------------------------
// Job Post Data
// -----------------------------
interface JobPost {
    id: number
    status: string
    title: string
    description: string
    banner: string
    work_mode: string
    seniority_level: string
    employment_type: string
    is_paid: boolean | number | null
    pay_amount: number | null
    pay_frequency: string
    expire_date: string
    duration: string
    apply_now_link: string
    additional_links: Array<{ text: string; url: string }>
}
const jobPost = ref<JobPost|null>(null);
const fetchJobPost = async (id: string) => {
    isInitiallyLoading.value = true;
    
    try {
        const { data: response } = await ApiService.request(
            'get', 
            `/api/job-posts/${id}`, 
            {}, 
            {
                headers: {
                    "Content-Type": "application/json",
                },
            }
        );

        console.log('response?.success', response?.success);
        if (response?.success) {
            jobPost.value = response.data;
            await nextTick();
        } else {
            showErrorAlert(response?.message);
            router.push({ name: 'company', query: { tab: 'JobsandOpportunities' } });
        }
    } catch (error: any) {
        console.log('error :>> ', error);
        const message = error.response?.data?.message;
        
        setErrors({}); // For initial load, clear all errors immediately
        showErrorAlert(message);
        router.push({ name: 'company', query: { tab: 'JobsandOpportunities' } });

        console.log('message :>> ', message);
    } finally {
        isInitiallyLoading.value = false;
    }
};

// ----------------------------------------------------------
// Fetch Data on Load
// ----------------------------------------------------------
onMounted(async () => {
    if (jobPostId) {
        await fetchJobPost(jobPostId);
    } else {
        isInitiallyLoading.value = false;
    }
});

// VEE VALIDATE
const schema = yup.object({
    status: yup
        .string()
        .required('Status is required'),

    title: yup
        .string()
        .required('Job title is required')
        .max(255, 'Job title must be less than 255 characters'),

    work_mode: yup
        .string()
        .required('Work mode is required'),

    seniority_level: yup
        .string()
        .required('Seniority level is required'),

    employment_type: yup
        .string()
        .required('Employment type is required'),

    is_paid: yup
        .boolean()
        .typeError('Please specify if the job is paid or not')
        .required('Please specify if the job is paid or not'),

    pay_amount: yup
        .number()
        .transform((val, orig) => (orig === '' ? undefined : Number(orig)))
        .typeError('Pay amount must be a number')
        .when('is_paid', {
            is: true,
            then: schema =>
            schema
                .required('Pay amount is required when paid')
                .min(0, 'Pay amount must be at least 0'),
            otherwise: schema => schema.notRequired().nullable(),
        }),

    pay_frequency: yup
        .string()
        .when('is_paid', {
            is: 1,
            then: schema => schema.required('Pay frequency is required when paid'),
            otherwise: schema => schema.notRequired().nullable(),
        }),

    expire_date: yup
        .date()
        .typeError('Expire date must be a valid date')
        .required('Expire date is required'),

    apply_now_link: yup
        .string()
        .url('Invalid URL')
        .required('Apply now link is required'),

    additional_links: yup
        .array()
        .of(yup.object({
            text: yup.string().max(255, 'text must be less than 255 characters').required('Text is required'),
            url: yup.string().url('Invalid URL').required('URL is required')
        }))
        .nullable()
});

configure({
    validateOnBlur: false,
    validateOnChange: false,
    validateOnInput: false,
    validateOnModelUpdate: false,
});

interface FormValues {
    status: string
    title: string
    description: string
    banner: string | null
    work_mode: string
    seniority_level: string
    employment_type: string
    is_paid: boolean | number | null
    pay_amount: number | null
    pay_frequency: string
    expire_date: string
    duration: string
    apply_now_link: string
    additional_links: Array<{ text: string; url: string }>
}
const { handleSubmit, setValues, setErrors, values, resetForm } = useForm<FormValues>({
    validationSchema: schema,
    initialValues: {
        status: '',
        title: '',
        description: '',
        banner: '',
        work_mode: '',
        seniority_level: '',
        employment_type: '',
        is_paid: 1,
        pay_amount: null,
        pay_frequency: '',
        expire_date: '',
        duration: '',
        apply_now_link: '',
        additional_links: [],
    },
    validateOnMount: false,
});

watch(jobPost, async (newJobPost) => {
    isLoading.value = true;
    setValues({
        status: newJobPost?.status || '',
        title: newJobPost?.title || '',
        description: newJobPost?.description || '',
        banner: newJobPost?.banner || '',
        work_mode: newJobPost?.work_mode || '',
        seniority_level: newJobPost?.seniority_level || '',
        employment_type: newJobPost?.employment_type || '',
        is_paid: newJobPost?.is_paid ?? null,
        pay_amount: newJobPost?.pay_amount || '',
        pay_frequency: newJobPost?.pay_frequency || '',
        expire_date: newJobPost?.expire_date || '',
        duration: newJobPost?.duration || '',
        apply_now_link: newJobPost?.apply_now_link || '',
        additional_links: newJobPost?.additional_links || [],
    });
    if (isInitiallyLoading.value) {
        setErrors({});
    }
    await nextTick();
    isLoading.value = false;
}, { immediate: true, deep: true });

const closeAfterSave = ref(false);

const jobPostFormSave = handleSubmit(async (values) => {
    if (jobPostId) {
        await updateJobPost(jobPostId);
    } else {
        await storeJobPost();
    }
});

const jobPostFormSaveAndClose = async () => {
    closeAfterSave.value = true;

    await jobPostFormSave();
};

const closePage = () => {
    router.push({ name: 'company', query: { tab: 'JobsandOpportunities' } });
    closeAfterSave.value = false;
};

const storeJobPost = async () => {
    isLoading.value = true;

    if (!currentUser?.company_id) {
        isLoading.value = false;
        return;
    }

    try {
        console.log(values);
        
        const { data: response } = await ApiService.request(
            'post', 
            `/api/job-posts`, 
            values, 
            {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            }
        );

        if (response?.success) {
            resetForm();

            if (closeAfterSave.value) {
                closePage();
            } else {
                router.push({ name: 'company.jobs-opportunities.edit', params: { id: response.data.id } });
            }

            showSuccessAlert(response?.message || 'Saved successfully');
        } else {
            showErrorAlert(response?.message);
        }
    } catch (error: any) {
        console.log('error :>> ', error);
        const message = error.response?.data?.message;

        if (error.response?.status === 422) {
            const validationErrors = normalizeBackendErrors(error.response.data?.errors || {});
            setErrors(validationErrors);
            
            const firstField = Object.keys(validationErrors)[0];
            
            const input = document.querySelector(`[name="${firstField}"]`) as HTMLInputElement | null;
            if (input) input.focus();
        } else {
            showErrorAlert(message);

            console.log('message :>> ', message);
        }
    } finally {
        isLoading.value = false;
    }
};

const updateJobPost = async (id: string|number) => {
    isLoading.value = true;

    if (!currentUser?.company_id) {
        isLoading.value = false;
        return;
    }

    try {
        console.log(values);
        
        const { data: response } = await ApiService.request(
            'post', 
            `/api/job-posts/${id}`, 
            { ...values, _method: 'PUT' }, 
            {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            }
        );

        if (response?.success) {
            jobPost.value = response.data;

            if (closeAfterSave.value) {
                closePage();
            }

            showSuccessAlert(response?.message || 'Updated successfully');
        } else {
            showErrorAlert(response?.message);
        }
    } catch (error: any) {
        console.log('error :>> ', error);
        const message = error.response?.data?.message;

        if (error.response?.status === 422) {
            const validationErrors = normalizeBackendErrors(error.response.data?.errors || {});
            setErrors(validationErrors);
            
            const firstField = Object.keys(validationErrors)[0];
            
            const input = document.querySelector(`[name="${firstField}"]`) as HTMLInputElement | null;
            if (input) input.focus();
        } else {
            showErrorAlert(message);

            console.log('message :>> ', message);
        }
    } finally {
        isLoading.value = false;
    }
};
</script>


<style scoped>
.form-label {
    color: #000000 !important;
}

.form-control {
    color: #000000 !important;
}
</style>