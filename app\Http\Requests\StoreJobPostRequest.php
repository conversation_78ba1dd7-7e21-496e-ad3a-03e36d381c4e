<?php

namespace App\Http\Requests;

use App\JobPost;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreJobPostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = request()->user();

        if (!$user) return false;

        return $user->can('create', JobPost::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => ['required', 'string', Rule::in(JobPost::STATUSES)],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'banner' => array_merge( ['nullable'] , (is_string(request()->banner) ? [] :  ['file', 'mimes:png,jpg,jpeg']) ),
            'work_mode' => ['required', 'string', Rule::in(JobPost::WORK_MODES)],
            'seniority_level' => ['required', 'string', Rule::in(JobPost::SENIORITY_LEVELS)],
            'employment_type' => ['required', 'string', Rule::in(JobPost::EMPLOYMENT_TYPES)],
            'is_paid' => ['required', 'boolean'],
            'pay_amount' => ['required_if:is_paid,1', 'nullable', 'numeric', 'min:0'],
            'pay_frequency' => ['required_if:is_paid,1', 'nullable', 'string', Rule::in(JobPost::PAY_FREQUENCIES)],
            'expire_date' => ['required', 'date' ,'after_or_equal:'.date('Y-m-d')],
            'duration' => ['nullable', 'string'],
            'apply_now_link' => ['required', 'url'],
            'additional_links' => ['nullable', 'array'],
            'additional_links.*.text' => ['required', 'string', 'max:255'],
            'additional_links.*.url' => ['required', 'url', 'max:65000'],
        ];
    }

    public function messages()
    {
        return [
            'additional_links.*.text.required' => 'Each link must include a button text.',
            'additional_links.*.text.string' => 'The text is invalid.',
            'additional_links.*.text.max' => 'The text may not be longer than 255 characters.',
            'additional_links.*.url.required' => 'Each link must include a url.',
            'additional_links.*.url.url' => 'Please provide a valid URL for link.',
            'additional_links.*.url.max' => 'The url may not be longer than 65000 characters.',
            'pay_amount.required_if' => 'The pay amount field is required when the job is paid.',
            'pay_frequency.required_if' => 'The pay frequency field is required when the job is paid.',
        ];
    }
}
