<template>
    <div class="card">
        <div class="card-header border-0 px-xl-0">
            <p class="card-title fs-1">About {{ name }}</p>
        </div>
        <div class="card-body px-xl-0">
           <div v-html="description"></div>
            <!-- <div class="about-logo">
                <img :src="logo" class="my-5 w-100"  style="max-height: 647px; object-fit: contain;"/>
            </div> -->
        </div>

    </div>

</template>

<script>
import { defineComponent, ref, onMounted, computed, watch } from 'vue';

export default defineComponent({
    props: {
        company: {
            type: Object,
            default: null,
        },
    },

    setup(props) {
        let name = props.company?.detail?.name ;
        let description = props.company?.detail?.description;
        let logo = props.company?.detail?.logo_full_path
        return {
            name,
            description,
            logo
        }

    }

})

</script>