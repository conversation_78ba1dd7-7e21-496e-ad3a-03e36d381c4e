<?php

namespace App\Services;

use App\Http\Resources\JobPostResource;
use App\JobPost;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class JobPostService {
    /**
     * Store new Job Post data
     *
     * @param integer $companyId
     * @param integer $authorId
     * @param array $data
     * @param boolean $wantsResource
     * @param array $with
     * @return array
     */
    public function store(int $companyId, int $authorId, array $data, $wantsResource = false, array $with = [])
    {
        $insertData = Arr::only($data, [
            'status',
            'title',
            'description',
            'work_mode',
            'seniority_level',
            'employment_type',
            'is_paid',
            'pay_amount',
            'pay_frequency',
            'expire_date',
            'duration',
            'apply_now_link',
            'additional_links',
        ]) + [
            'company_id' => $companyId,
            'author_id' => $authorId,
            'banner' => $this->handleFileUpload($data['banner'] ?? null, null),
        ];

        $jobPost = JobPost::create($insertData);

        if (!empty($with)) {
            $jobPost->load($with);
        }

        return [
            'success' => true,
            'message' => 'Job data saved successfully',
            'data' => $wantsResource ? (new JobPostResource($jobPost)) : $jobPost
        ];
    }

    /**
     * Update Job Post data
     *
     * @param \App\JobPost $jobPost
     * @param array $data
     * @param boolean $wantsResource
     * @param array $with
     * @return array
     */
    public function update(JobPost $jobPost, array $data, $wantsResource = false, array $with = [])
    {
        $updateData = Arr::only($data, [
            'status',
            'title',
            'description',
            'banner',
            'work_mode',
            'seniority_level',
            'employment_type',
            'is_paid',
            'pay_amount',
            'pay_frequency',
            'expire_date',
            'duration',
            'apply_now_link',
            'additional_links',
        ]);

        $updateData['banner'] = $this->handleFileUpload($data['banner'] ?? null, $jobPost->banner);

        $jobPost->update($updateData);

        $jobPost = $jobPost->refresh();

        if (!empty($with)) {
            $jobPost->load($with);
        }

        return [
            'success' => true,
            'message' => 'Job data updated successfully',
            'data' => $wantsResource ? (new JobPostResource($jobPost)) : $jobPost
        ];
    }

    public function syncRelatedData(JobPost $jobPost, array|string $relationNames, array $data)
    {
        $syncCallback = function($jobPost, $relationName, $data) {
            if (array_key_exists($relationName, $data) && method_exists(JobPost::class, $relationName)) {
                $data[$relationName] = (!empty($data[$relationName]) ? $data[$relationName] : []);

                $jobPost->{$relationName}()->sync((array) $data[$relationName]);
            }
        };
        
        if (is_array($relationNames)) {
            foreach ($relationNames as $relationName) {
                $syncCallback($jobPost, $relationName, $data);
            }
        } else {
            $syncCallback($jobPost, $relationNames, $data);
        }
    }

    protected function handleFileUpload($file, ?string $oldPath): ?string
    {
        if ($file instanceof \Illuminate\Http\UploadedFile && $file->isValid()) {
            if ($oldPath && Storage::exists($oldPath)) {
                Storage::delete($oldPath);
            }

            return $file->store('attachments/job-posts', ['visibility' => 'public']);
        }

        if ($file === null && $oldPath) {
            if (Storage::exists($oldPath)) {
                Storage::delete($oldPath);
            }

            return null;
        }

        return $oldPath; // keep old file if no new one uploaded
    }
}