<?php

namespace App\Http\Controllers\Vue;

use App\JobPost;
use App\Library\CustomClasses\Indeed;
use Illuminate\Http\Request;
use Auth;

class JobsfinderController extends Controller
{

public function index(Request $request)
{
    $query = JobPost::with('company:id,name')
        ->select('id', 'company_id', 'title', 'employment_type', 'pay_frequency', 'pay_amount', 'created_at');

    if ($request->has('title') && !empty($request->input('title'))) {
        $query->where('title', 'like', '%' . $request->input('title') . '%');
        
    }

    if ($request->has('employment_type') && !empty($request->input('employment_type'))) {
        $query->where('employment_type', 'like', '%' . $request->input('employment_type') . '%');
        
    }
    if ($request->has('pay_frequency') && !empty($request->input('pay_frequency'))) {
        $query->where('pay_frequency', 'like', '%' . $request->input('pay_frequency') . '%');
        
    }

  
  if ($request->has('jobtype') && !empty($request->input('jobtype'))) {
        $jobTypes = (array) $request->input('jobtype');
        if (!empty($jobTypes)) {
            $query->whereIn('employment_type', $jobTypes);
        }
    }

    $perPage = $request->input('per_page', 3); 
    $jobs = $query->paginate($perPage);

    return response()->json($jobs);
}
    public function search(Request $request)
    {
        // dd($request->all());
        $userip = request()->ip();
        $useragent = request()->header('User-Agent');
        $what = request('what');
        $where = request('where');
        $sort = request('sort');
        $distance = request('distance');
        $jobtype = request('jobtype');


        if (!$what) {
            $lastplan = Auth::user()->lastPlan();
            if (@$lastplan->industries) {
                $industries = $lastplan->industries()->select('industry_categories.id', 'name')->get()->append('search_keyword')->pluck('search_keyword')->implode(', ');
                $what = $industries;
            }
        }

        if (!$where) {
            $location = Auth::user()->location();
            if (isset($location->vicinity) && !empty($location->vicinity)) {
                $where =  str_replace(($location->vicinity), '', $location->address);
            } else {
                $where = $location->address;
            }
        }

        $client = new Indeed(config('services.indeed.id'));
        // dd($client);

        $page = $request->input('page', 1);
        $perPage = 24; // Number of items per page

        $params = array(
            "v" => "2",     // API Version
            'q' => $what,
            'l' => $where,
            "co" => "au",      // Country Code default US
            "userip" => $userip, // user's IP Address
            "useragent" => $useragent,    // user agent
            'sort' => $sort,
            'radius' => $distance,
            'jt' => $jobtype,
            'limit' => $perPage,
            'start' => ($page - 1) * $perPage,
        );
        // dd($params);
        $results = $client->search($params);
        // dd($results);
        return $results['results'];
        // $queries = explode(', ', $what);
        // $limit = 30;  // Default limit

        // if (count($queries) > 1) {
        //     $limit = intdiv(30, count($queries));
        // }

        // $data = [];


        // foreach ($queries as $key => $q) {
        //     $params = array(
        //         "v" => "2",     // API Version
        //         'q' => $what,
        //         'l' => $where,
        //         "co" => "au",      // Country Code default US
        //         "userip" => $userip, // user's IP Address
        //         "useragent" => $useragent,    // user agent
        //         'limit' => $limit,
        //         'sort' => $sort,
        //         'radius' => $distance,
        //         'jt' => $jobtype,
        //         // 'start' => 23,
        //     );
        //     $results = $client->search($params)['results'];
        //     $data = array_merge($data, $results);
        // }

        // if ($sort == 'date') {
        //     usort($data, function ($a, $b) {
        //         $dateA = strtotime($a['date']);
        //         $dateB = strtotime($b['date']);

        //         if ($dateA == $dateB) {
        //             return 0;
        //         }

        //         return ($dateA > $dateB) ? -1 : 1;
        //     });
        // } else {
        //     shuffle($data);
        // }
        // return $data;
    }



    public function banner()
    {
        return Banner::whereType('Job Finder')->first();
    }
}
