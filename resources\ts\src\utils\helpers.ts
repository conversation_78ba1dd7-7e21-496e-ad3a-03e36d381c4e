import moment from "moment";
import Swal from "sweetalert2";

export function formatTimestampBrowserLocale(timestamp: number): string {
  const locale = navigator.language || 'en-US';
  return moment(timestamp).locale(locale).format('L LT');
}

export function normalizeBackendErrors(errors) {
  const normalized = {};

  Object.entries(errors).forEach(([key, messages]) => {
    // Replace `.number.` with `[number].`
    const normalizedKey = key.replace(/\.(\d+)\./g, '[$1].');
    normalized[normalizedKey] = messages;
  });

  return normalized;
}

export function showErrorAlert(message?: string) {
  return Swal.fire({
    title: "Alert",
    text: message || 'Something went wrong, Try again later.',
    icon: "error",
    buttonsStyling: false,
    confirmButtonText: "Ok",
    showCancelButton: false,
    timer: 3000,
    timerProgressBar: true,
    customClass: {
      confirmButton: "btn fw-semibold btn-light-primary rounded",
    },
  });
}

export function showSuccessAlert(message: string) {
  return Swal.fire({
    title: "Success",
    text: message || 'Action performed successfully',
    icon: "success",
    buttonsStyling: false,
    confirmButtonText: "Ok",
    showCancelButton: false,
    timer: 3000,
    timerProgressBar: true,
    customClass: {
      confirmButton: "btn fw-semibold btn-light-primary rounded",
    },
  });
}