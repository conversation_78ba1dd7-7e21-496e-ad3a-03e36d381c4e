<?php

namespace App\Http\Controllers\Vue;

use App\Http\Controllers\Controller;
use App\JobPost;
use App\Http\Requests\StoreJobPostRequest;
use App\Http\Requests\UpdateJobPostRequest;
use App\Http\Resources\JobPostResource;
use App\Services\JobPostService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class JobPostController extends Controller
{
    public function __construct(
        protected JobPostService $jobPostService
    )
    {
        
    }

    /**
     * Display a listing of the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', JobPost::class);
        
        /** @var \App\User $user */
        $authUser = Auth::user();
        $perPage = $request->input('per_page', 10);
        $page = $request->input('page', 1);
        $companyId = $authUser->company_id;

        if ($authUser->isAdmin()) {
            $companyId = $request->company_id ?: null;
        }

        $data = JobPost::query()
            ->select('id', 'title', 'status', 'work_mode' , 'expire_date', 'created_at')
            ->when($companyId, function ($query) use ($companyId) {
                return $query->where('company_id', $companyId);
            })
            ->paginate($perPage, ['*'], 'page', $page)
            ->through(function ($jobPost) {
                return [
                    'id' => $jobPost->id,
                    'title' => $jobPost->title,
                    'status' => [
                        'label' => $jobPost->dynamic_status ? ucwords($jobPost->dynamic_status) : null,
                        'state' => match ($jobPost->dynamic_status) {
                            'published' => 'success',
                            'draft' => 'warning',
                            'expired' => 'danger',
                            default => 'secondary',
                        }
                    ],
                    'work_mode' => $jobPost->work_mode ? ucwords($jobPost->work_mode) : null,
                    'expire_date' => $jobPost->expire_date ? Carbon::parse($jobPost->expire_date)->format('d/m/Y') : null,
                    'created_at' => $jobPost->created_at ? Carbon::parse($jobPost->created_at)->format('d/m/Y') : null,
                ];
            });
            
        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\StoreJobPostRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StoreJobPostRequest $request)
    {
        $this->authorize('create', JobPost::class);

        /** @var \App\User $user */
        $authUser = Auth::user();

        $res = $this->jobPostService->store(
            companyId: $authUser->company_id,
            authorId: $authUser->id,
            data: $request->validated(),
            wantsResource: true,
        );

        return response()->json($res);
    }

    /**
     * Display the specified resource.
     *
     * @param \App\JobPost $jobPost
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(JobPost $jobPost)
    {
        $this->authorize('view', $jobPost);

        return response()->json([
            'success' => true,
            'data' => new JobPostResource($jobPost)
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateJobPostRequest $request
     * @param \App\JobPost $jobPost
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateJobPostRequest $request, JobPost $jobPost)
    {
        $res = $this->jobPostService->update(
            jobPost: $jobPost,
            data: $request->validated(),
            wantsResource: true,
        );

        return response()->json($res);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\JobPost $jobPost
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(JobPost $jobPost)
    {
        $this->authorize('delete', $jobPost);

        $jobPost->delete();

        return response()->json([
            'success' => true,
            'message' => 'Job post deleted successfully'
        ]);
    }
}
