<?php

namespace App;

use App\Traits\HasIntercomUserInfo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Auth;
use Overtrue\LaravelFavorite\Traits\Favoriteable;

class Company extends Model
{
    use Cachable , Favoriteable;
    use HasIntercomUserInfo;

    protected $table = "users";

    /**
     * Don't auto-apply mass assignment protection.
     *
     * @var array
     */
    protected $guarded = [];
    protected $appends = ['favourite'];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['detail'];

    protected static function booted()
    {
        static::deleting(function ($company) {
            if ($company->detail) {
                $company->detail->delete();
            }
        });

        static::addGlobalScope('company', function (Builder $builder) {
            $companyRoleId = Cache::rememberForever('companyRoleId', function () {
                return Role::whereName('Company')->value('id');
            });
            $builder->where('role_id', $companyRoleId);
        });
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function detail()
    {
        return $this->hasOne(SchoolDetail::class, 'school_id');
    }

    public function states()
    {
        return $this->belongsToMany(State::class, 'company_state');
    }

    public function industries()
    {
        return $this->belongsToMany(IndustryCategory::class, 'company_industry_category');
    }

    public function industryunits()
    {
        return $this->belongsToMany(Industryunit::class, 'company_industryunit');
    }

    public function workexperienceTemplates()
    {
        return $this->belongsToMany(WorkexperienceTemplate::class, 'company_workexperience_template');
    }

    public function skillstrainingTemplates()
    {
        return $this->belongsToMany(SkillstrainingTemplate::class, 'company_skillstraining_template');
    }

    public function lessons()
    {
        return $this->belongsToMany(Lesson::class, 'company_lesson');
    }

    public function badges()
    {
        return $this->belongsToMany(Badge::class, 'badge_company', 'company_id', 'badge_id')
                    ->withTimestamps();
    }

    public function pages()
    {
        return $this->hasMany(CompanyPage::class, 'company_id');
    }

    public function path()
    {
        return "companies";
    }

    public function gameplans()
    {
        return $this->belongsToMany(Gameplan::class, 'gameplan_companies')
                    ->withPivot('company_name');
    }

     protected function favourite(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->hasBeenFavoritedBy(Auth::user())
        );
    }
}
