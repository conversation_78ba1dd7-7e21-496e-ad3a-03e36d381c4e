<template>
    <div class="d-flex align-items-center justify-content-between">
        <h3 class="mt-15 ms-5"><span v-if="!isLoading">{{ pagination.total || 'No' }} Jobs and Opportunities found</span></h3>

        <button
            type="button"
            class="btn btn-secondary d-flex align-items-center gap-2 mt-5 py-4"
            @click="goToCreatePage"
            data-kt-menu-trigger="click"
            data-kt-menu-placement="bottom-end"
        >
            <i class="bi bi-plus fs-1"></i>
            Add New Job or Opportunity
        </button>
    </div>
    <div class="card  mt-5">
        <div class="card-body ">
            <KTDataTable
                :header="tableHeader"
                :items-per-page-dropdown-enabled="false"
                :data="tableData"
                :loading="isLoading"
                :total="pagination.total"
                :current-page="pagination.currentPage"
                :items-per-page="pagination.perPage"
                @page-change="fetchJobPosts"
                class="decor-dataTable"
                table-class="table table-row-bordered border table-row-dashed gy-4 align-middle fw-bold"
                table-body-class="fs-6"
            >
                <template v-slot:title="{ row }">
                    <div class="ps-10">
                        {{ row.title }}
                    </div>
                </template>
                <template v-slot:work_mode="{ row }">
                    {{ row.work_mode }}
                </template>
                <template v-slot:created_at="{ row }">
                    {{ row.created_at }}
                </template>
                <template v-slot:expire_date="{ row }">
                    {{ row.expire_date }}
                </template>
                <template v-slot:status="{ row }">
                    <span :class="`badge badge-light-${row.status.state}`">
                    {{row.status.label}}
                    </span>
                </template>
                <template v-slot:actions="{ row: job }">
                    <div class="d-flex justify-content-start p-0 gap-2">
                        <!-- Edit -->
                        <button
                            class="btn btn-icon btn-sm"
                            @click="goToEditPage(job.id)"
                            title="Edit"
                        >
                            <i class="bi bi-pencil-fill fs-3 color-decor-grey"></i>
                        </button>


                        <!-- Delete -->
                        <button
                            class="btn btn-icon btn-sm"
                            title="Delete"
                            @click="deleteJobPost(job.id)"
                        >
                            <i class="bi bi-trash fs-3 color-decor-grey"></i>
                        </button>
                    </div>
                </template>
            </KTDataTable>
        </div>
    </div>

</template>

<script lang="ts" setup>
import KTDataTable from "@/components/kt-datatable/KTDataTable.vue";
import ApiService from "@/core/services/ApiService";
import { showErrorAlert, showSuccessAlert } from "@/utils/helpers";
import Swal from "sweetalert2";
import { nextTick, onMounted, ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const isLoading = ref(false);

const tableHeader = ref([
    {
        columnName: 'Title',
        columnLabel:  'title',
        thClass: 'min-w-200px ps-10',
    },
    {
        columnName: 'Type',
        columnLabel:  'work_mode',
        thClass: 'min-w-150px',
    },
    {
        columnName: 'Date Posted',
        columnLabel:  'created_at',
        thClass: 'min-w-120px',
    },
    {
        columnName: 'Expiry Date',
        columnLabel:  'expire_date',
        thClass: 'min-w-120px',
    },
    {
        columnName: 'Status',
        columnLabel:  'status',
        thClass: 'min-w-100px',
    },
    {
        columnName: 'Actions',
        columnLabel:  'actions',
        thClass: 'min-w-80px pe-10',
    },
]);

const tableData = ref([]);
const pagination = ref({
  currentPage: 1,
  perPage: 10,
  total: 0,
});

const fetchJobPosts = async (page = 1, perPage = pagination.value.perPage) => {
    isLoading.value = true;

    try {
        const { data: response } = await ApiService.request('get', `/api/job-posts`, {
            page,
            per_page: perPage,
        });

        if (response?.success) {
            tableData.value = response.data?.data;
            pagination.value.total = response.data?.total || 0;
            pagination.value.perPage = response.data?.per_page || 10;
            if (pagination.value.currentPage !== page) {
                pagination.value.currentPage = response.data?.current_page || 1;
            }
        } else {
            tableData.value = [];
        }
    } catch (error: any) {
        tableData.value = [];
        const message = error.response?.data?.message;

        if (message) {
            showErrorAlert(message);
        }
        console.error("Error fetching data:", error);
    } finally {
        await nextTick();
        isLoading.value = false;
    }
};

onMounted(async () => {
    await fetchJobPosts();
});

const goToCreatePage = () => {
    router.push({ name: "company.jobs-opportunities.create" });
};

const goToEditPage = (id: number|string) => {
    router.push({ name: "company.jobs-opportunities.edit", params: { id } });
};

const deleteJobPost = async (id: number|string) => {
    const swalResult = await Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        customClass: {
            confirmButton: "btn fw-semibold btn-light-primary rounded",
            cancelButton: "btn fw-semibold btn-secondary rounded",
        },
    });

    if (!swalResult.isConfirmed) {
        return;
    }

    isLoading.value = true;

    try {
        const { data: response } = await ApiService.request('delete', `/api/job-posts/${id}`);

        if (response?.success) {
            await fetchJobPosts(pagination.value.currentPage, pagination.value.perPage);
            showSuccessAlert(response.message || "Job post deleted successfully.");
        } else {
            showErrorAlert(response?.message || "Failed to delete the job post.");
        }
    } catch (error: any) {
        const message = error.response?.data?.message;

        if (error.response?.status === 404 && message) {
            showErrorAlert(message);
        } else {
            console.error("Error deleting job post:", error);
            showErrorAlert("An error occurred while deleting the job post.");
        }
    } finally {
        await nextTick();
        isLoading.value = false;
    }
};
</script>

<style scoped>
.decor-dataTable {
    
}
.color-decor-grey {
    color: #606060;
}
</style>
