<template>
  <div class="row g-10">
    <div v-for="job in jobs" :key="job.id" class="col-12 col-xl-4">
      <div class="card mt-5 border">
        <div class="card-header border-0 align-content-center pb-4 pt-7">
          <p class="mb-0 text-muted mt-5">{{ job.posted }}</p>
          <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="25" cy="25" r="20" fill="black" />
            <svg width="45" height="40" viewBox="-2 -4 34 33" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M17 11.1388C20.8834 7.18512 30.5926 14.1037 17 23C3.40738 14.1046 13.1166 7.18512 17 11.1388Z"
                fill="white" />
            </svg>
          </svg>
        </div>
        <div class="card-header border-0">
          <div class="card-title flex-column">
            <h3 class="mb-5">{{ job.company }}</h3>
            <h1>{{ job.title }}</h1>
          </div>
          <div class="bg-light card-div" style="width: 138px; height: 60px;"></div>
        </div>
        <div class="card-body">
          <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
            <a href="#" class="d-flex align-items-center text-gray-700 text-hover-primary me-10 mb-2">
              <svg class="me-2" width="14" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M6 16C6 16 12 10.314 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 2.37122e-08 4.4087 0 6C0 10.314 6 16 6 16ZM6 9C5.20435 9 4.44129 8.68393 3.87868 8.12132C3.31607 7.55871 3 6.79565 3 6C3 5.20435 3.31607 4.44129 3.87868 3.87868C4.44129 3.31607 5.20435 3 6 3C6.79565 3 7.55871 3.31607 8.12132 3.87868C8.68393 4.44129 9 5.20435 9 6C9 6.79565 8.68393 7.55871 8.12132 8.12132C7.55871 8.68393 6.79565 9 6 9Z"
                  fill="#606060" />
              </svg>
              {{ job.location }}
            </a>
            <a href="#" class="d-flex align-items-center text-gray-700 text-hover-primary me-10 mb-2">
              <svg class="me-2" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M16 8.5C16 10.6217 15.1571 12.6566 13.6569 14.1569C12.1566 15.6571 10.1217 16.5 8 16.5C5.87827 16.5 3.84344 15.6571 2.34315 14.1569C0.842855 12.6566 0 10.6217 0 8.5C0 6.37827 0.842855 4.34344 2.34315 2.84315C3.84344 1.34285 5.87827 0.5 8 0.5C10.1217 0.5 12.1566 1.34285 13.6569 2.84315C15.1571 4.34344 16 6.37827 16 8.5ZM8 4C8 3.86739 7.94732 3.74021 7.85355 3.64645C7.75979 3.55268 7.63261 3.5 7.5 3.5C7.36739 3.5 7.24021 3.55268 7.14645 3.64645C7.05268 3.74021 7 3.86739 7 4V9.5C7.00003 9.58813 7.02335 9.67469 7.06761 9.75091C7.11186 9.82712 7.17547 9.89029 7.252 9.934L10.752 11.934C10.8669 11.9961 11.0014 12.0108 11.127 11.9749C11.2525 11.9391 11.3591 11.8556 11.4238 11.7422C11.4886 11.6288 11.5065 11.4946 11.4736 11.3683C11.4408 11.2419 11.3598 11.1334 11.248 11.066L8 9.21V4Z"
                  fill="#606060" />
              </svg>
              {{ job.type }}
            </a>
            <a href="#" class="d-flex align-items-center text-gray-700 text-hover-primary me-10 mb-2">
              <svg class="me-2" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M0 4.5C0 4.23478 0.105357 3.98043 0.292893 3.79289C0.48043 3.60536 0.734784 3.5 1 3.5H15C15.2652 3.5 15.5196 3.60536 15.7071 3.79289C15.8946 3.98043 16 4.23478 16 4.5V12.5C16 12.7652 15.8946 13.0196 15.7071 13.2071C15.5196 13.3946 15.2652 13.5 15 13.5H1C0.734784 13.5 0.48043 13.3946 0.292893 13.2071C0.105357 13.0196 0 12.7652 0 12.5V4.5ZM3 4.5C3 5.03043 2.78929 5.53914 2.41421 5.91421C2.03914 6.28929 1.53043 6.5 1 6.5V10.5C1.53043 10.5 2.03914 10.7107 2.41421 11.0858C2.78929 11.4609 3 11.9696 3 12.5H13C13 11.9696 13.2107 11.4609 13.5858 11.0858C13.9609 10.7107 14.4696 10.5 15 10.5V6.5C14.4696 6.5 13.9609 6.28929 13.5858 5.91421C13.2107 5.53914 13 5.03043 13 4.5H3Z"
                  fill="#606060" />
                <path
                  d="M8 10.5C8.53043 10.5 9.03914 10.2893 9.41421 9.91421C9.78929 9.53914 10 9.03043 10 8.5C10 7.96957 9.78929 7.46086 9.41421 7.08579C9.03914 6.71071 8.53043 6.5 8 6.5C7.46957 6.5 6.96086 6.71071 6.58579 7.08579C6.21071 7.46086 6 7.96957 6 8.5C6 9.03043 6.21071 9.53914 6.58579 9.91421C6.96086 10.2893 7.46957 10.5 8 10.5Z"
                  fill="#606060" />
              </svg>
              {{ job.pay_frequency }}
            </a>
          </div>
        </div>
        <div class="d-flex flex-wrap justify-content-between fw-semibold fs-6 mb-6 px-10">
          <button class="border-0 px-10 py-4 rounded fs-6 bg-light transition">
            View Job
          </button>
          <button class="border-0 px-14 py-4 rounded fs-6 bg-grey transition">
            Apply
          </button>
        </div>
      </div>
    </div>
   
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, onMounted, watch } from 'vue';

export default defineComponent({
  name: 'BlockView',
  props: {
    jobs: {
      type: Array as PropType<any[]>,
      required: true,
    },
    noSearchResult: {
      type: Boolean,
      required: true,
    },
    noJobsLeft: {
      type: Boolean,
      required: true,
    },
    loadMoreJobs: {
      type: Function,
      required: true,
    },
  },
  setup(props) {

 
    return {};
  },
});
</script>

<style scoped>
.bg-grey {
  background-color: #e5e5e5;
}
.transition {
  transition: all 0.3s ease;
}
.transition:hover {
  opacity: 0.8;
}
</style>