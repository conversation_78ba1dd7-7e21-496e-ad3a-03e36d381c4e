<template>
    <div class="mt-5">
        <form @submit.prevent="onProfileFormSubmit()" ref="profileFormRef" @keydown.enter.prevent="onProfileFormSubmit()">
            <CardsSkeleton :is-loading="isInitiallyLoading" />

            <div class="row" v-if="!isInitiallyLoading">
                <!-- Left Column -->
                <div class="col-lg-9">
                    <!-- About Section -->
                    <div class="card mb-4 border  ">
                        <div class="card-header align-items-center ">
                            <p class=" card-title">About</p>
                            <button
                                type="button"
                                class="btn btn-secondary me-2"
                                @click="canEdit ? onProfileFormSubmit() : toggleEdit()"
                                data-kt-menu-trigger="click"
                                data-kt-menu-placement="bottom-end"
                                :disabled="isLoading"
                            >
                                {{ canEdit ? "Save" : "Update Details" }}
                                <span class="spinner-border spinner-border-sm" v-if="isLoading" aria-hidden="true"></span>
                            </button>
                        </div>

                        <div class="card-body">
                            <div>
                                <div class="row d-flex flex-column ">
                                    <!-- Company Title -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Company Title</label>
                                        <Field
                                            class="form-control"
                                            name="name"
                                            type="text"
                                            :disabled="!canEdit"
                                        >
                                        </Field>
                                        
                                        <ErrorMessage name="name" as="div" class="text-danger mt-1" />
                                        
                                    </div>

                                    <!-- Locations -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Locations</label>
                                        <CustomSelect
                                            :options="[
                                                { value: 'ny', label: 'New York' },
                                            ]"
                                            :multiple="true"
                                            :taggable="true"
                                            tag-placeholder="Add this as new location"
                                            placeholder="Search or add a location"
                                            label="label"
                                            track-by="value"
                                            :close-on-select="false"
                                            :clear-on-select="false"
                                            :disabled="!canEdit"
                                            :mode="'tags'"
                                        />
                                    </div>
                                </div>

                                <label class="form-label">Description</label>

                                <div>
                                    <Field v-slot="{ field }" name="description">
                                        <froala
                                            :tag="'textarea'"
                                            v-bind="field"
                                            :config="froalaConfig"
                                        ></froala>
                                    </Field>
                                    <ErrorMessage name="description" as="div" class="text-danger mt-1" />
                                    
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Associated Industries -->
                    <div class="card mb-4 border ">
                        <div class="card-header">
                            <p class="card-title">Associated Industries</p>
                        </div>
                        <div class="card-body">
                            <p class="form-label fs-5">Industries</p>
                            <p class="text-muted">Select up to 4 industries</p>

                            <Field v-slot="{ field }" name="industries">
                                <CustomSelect
                                    v-bind="field"
                                    :options="industryCategoryOptions"
                                    placeholder="Start typing to search..."
                                    :close-on-select="false"
                                    :disabled="!canEdit"
                                    :searchable="true"
                                    :required="true"
                                    mode="tags"
                                />
                            </Field>
                            <ErrorMessage name="industries" as="div" class="text-danger mt-1" />
                            
                        </div>
                    </div>

                    

                    <!-- Social Links -->
                    <div class="card mb-4 border ">
                        <div class="card-header">
                            <p class="card-title">Social Links</p>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Company URL</label>
                                    <Field
                                        type="text"
                                        name="url"
                                        class="form-control"
                                        placeholder="https://www.example.com"
                                        :disabled="!canEdit"
                                    />
                                    <ErrorMessage name="url" as="div" class="text-danger mt-1" />
                                    
                                </div>

                                <SocialLinksRepeater
                                    :disabled="!canEdit"
                                />
                            </div>
                        </div>
                    </div>

                </div>


                <!-- Right Side -->
                <div class="col-lg-3">
                    <div class="card mb-8 border">
                        <div>
                            <div class="card card-flush">

                                <div class="card-header">
                                    <p class="card-title">Status</p>

                                    <div class="card-toolbar">
                                        <div class="rounded-circle  w-15px h-15px" :class="company?.detail?.status === 'published' ? 'bg-success' : 'bg-warning'" id="kt_ecommerce_add_category_status">
                                        </div>
                                    </div>
                                </div>

                                <div class="card-body pt-0">
                                    <Field v-slot="{ field }" name="status">
                                        <CustomSelect
                                            v-bind="field"
                                            :options="[
                                                { value: 'draft', label: 'Draft' },
                                                { value: 'published', label: 'Published' },
                                            ]"
                                            :disabled="!canEdit"
                                            :can-clear="false"
                                            :required="true"
                                            mode="single"
                                            placeholder="Select status"
                                        />
                                    </Field>

                                    <ErrorMessage name="status" as="div" class="text-danger mt-1" />
                                    

                                    <div class="fs-7 mt-2" v-if="lastPublishedAt"> Last published on {{ lastPublishedAt }}</div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- Icon Image Card -->
                    <div class="card card-flush  mb-8 border">
                        <div class="card-header">
                            <p class="card-title">Icon image</p>
                        </div>
                        <div class="card-body text-center pt-0">
                            <ImageUploader
                                name="logo"
                                help-text="Set the icon image. Only *.png, *.jpg and *.jpeg image files are accepted"
                                accept=".png, .jpg, .jpeg"
                                :disabled="!canEdit"
                            />
                        </div>
                    </div>
                    <!-- Icon Image Card ./ -->


                    <!-- Banner Image Card -->
                    <div class="card card-flush  border">
                        <div class="card-header">
                            <p class="card-title">Banner image</p>
                        </div>

                        <div class="card-body text-center pt-0">
                            <ImageUploader
                                name="banner"
                                help-text="Set the banner image. Only *.png, *.jpg and *.jpeg image files are accepted"
                                accept=".png, .jpg, .jpeg"
                                :disabled="!canEdit"
                            />
                        </div>
                    </div>
                    <!-- Banner Image Card ./ -->

                </div>
            </div>
        </form>
    </div>
</template>


<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from "vue";
import ApiService from "@/core/services/ApiService";
import { useStore } from 'vuex';
import SocialLinksRepeater from "./SocialLinksRepeater.vue";
import { cloneDeep } from "lodash";
import ImageUploader from "@/components/common/ImageUploader.vue";
import { formatTimestampBrowserLocale, normalizeBackendErrors, showErrorAlert, showSuccessAlert } from "@/utils/helpers";
import { ErrorMessage, Field, useForm, configure } from "vee-validate";
import * as yup from 'yup';
import CustomSelect from "@/components/common/CustomSelect.vue";
import CardsSkeleton from "@/components/common/CardsSkeleton.vue";

// -----------------------------
// Core
// -----------------------------
const isLoading = ref(false);
const isInitiallyLoading = ref(false);

// -----------------------------
// Categories
// -----------------------------
const industryCategoryOptions = ref([]);
const fetchIndustryCategoryOptions = async () => {
    isLoading.value = true;

    try {
        const { data: response } = await ApiService.query(`/api/industry-categories`, { params : { as_label_value: true } });

        if (response?.success) {
            industryCategoryOptions.value = response.data;
        } else {
            industryCategoryOptions.value = [];
        }
    } catch (error: any) {
        console.error("Error fetching Industry Categories:", error);
    } finally {
        isLoading.value = false;
    }
};

// -----------------------------
// Company and Employer 
// -----------------------------
interface CompanyDetail {
    description: string|null,
    status: string|null,
    url: string|null,
    logo: string|null,
    banner: string|null,
    social_links: any[]|null
    last_published_at: any,
}
interface Company {
    name: string,
    description: string,
    industries: any[],
    detail: CompanyDetail
};
const store = useStore();
const currentUser = store.getters.currentUser;
const company = ref<Company|null>(null);
const lastPublishedAt = computed(() => {
    let dateTime = company.value?.detail?.last_published_at;
    return dateTime ? formatTimestampBrowserLocale(dateTime) : null;
});
const fetchCompany = async () => {
    isLoading.value = true;

    if (!currentUser?.company_id) {
        isLoading.value = false;
        return;
    }

    try {
        const { data: response } = await ApiService.get(`/api/companies/${currentUser.company_id}`);

        if (response?.success) {
            company.value = response.data;
        } else {
            company.value = null;
        }
    } catch (error: any) {
        company.value = null;
        const message = error.response?.data?.message;

        if (error.response?.status === 404 && message) {
            showErrorAlert(message);
        } else {
            console.error("Error fetching company:", error);
        }
    } finally {
        isLoading.value = false;
    }
};

// ----------------------------------------------------------
// Fetch Data on Load
// ----------------------------------------------------------
onMounted(async () => {
    isInitiallyLoading.value = true;
    await Promise.all([fetchIndustryCategoryOptions(), fetchCompany(), nextTick()]);
    isInitiallyLoading.value = false;
});

// -----------------------------
// Company Profile Form 
// -----------------------------
const initialSocialLinks = [
    { platform: "linkedin", url: null },
    { platform: "instagram", url: null },
    { platform: "facebook", url: null }
];
const profileFormRef = ref();
const canEdit = ref(false);
// FROALA EDITOR 
const editorInstance = ref();
const froalaConfig = {
    key: "hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",
    height: 300,
    attribution: false,
    events : {
        initialized: function () {
            editorInstance.value = this;
            if (!canEdit.value) {
                editorInstance.value?.edit?.off();
            }
        },
    }
};
watch(canEdit, (editable) => {
    if (editorInstance.value) {
        editable ? editorInstance.value.edit.on() : editorInstance.value.edit.off();
    }
});

// VEE VALIDATE
const schema = yup.object({
    name: yup
        .string()
        .required('Company title is required')
        .max(255, 'Company title must be less than 255 characters'),

    status: yup
        .string()
        .required('Status is required'),

    industries: yup
        .array()
        .min(1, 'Select at least 1 industry')
        .max(4, 'You can select up to 4 industries only'),

    url: yup
        .string()
        .url('Invalid URL'),

    social_links: yup.array()
        .of(yup.object({
            platform: yup.string().max(255, 'Platform must be less than 255 characters').nullable(),
            url: yup.string().url('Invalid URL').nullable()
        }))
        .nullable()
});

configure({
    validateOnBlur: false,
    validateOnChange: false,
    validateOnInput: false,
    validateOnModelUpdate: false,
});

const { handleSubmit, setValues, validate, setErrors } = useForm({
    validationSchema: schema,
    initialValues: {
        name: '',
        description: '',
        industries: [],
        status: '',
        url: '',
        logo: '',
        banner: '',
        social_links: cloneDeep(initialSocialLinks),
    },
    validateOnMount: false,
});

watch(company, async (newCompany: Company|null) => {
    isLoading.value = true;
    setValues({
        name: newCompany?.name || '',
        description: newCompany?.detail?.description || '',
        industries: newCompany?.industries?.map((item) => item.id) || [],
        status : newCompany?.detail?.status || '',
        logo : newCompany?.detail?.logo || '',
        banner : newCompany?.detail?.banner || '',
        url : newCompany?.detail?.url || '',
        social_links : newCompany?.detail?.social_links || cloneDeep(initialSocialLinks),
    });
    if (isInitiallyLoading.value) {
        setErrors({});
    }
    await nextTick();
    isLoading.value = false;
}, { immediate: true, deep: true });

const onProfileFormSubmit = handleSubmit(async (values) => {
    isLoading.value = true;

    if (!currentUser?.company_id) {
        isLoading.value = false;
        return;
    }

    try {
        console.log(values);
        
        const { data: response } = await ApiService.request(
            'post', 
            `/api/companies`, 
            values, 
            {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            }
        );

        if (response?.success) {
            company.value = response.data;
            canEdit.value = false;

            showSuccessAlert(response?.message || 'Updated successfully');
        } else {
            showSuccessAlert(response?.message);
        }
    } catch (error: any) {
        console.log('error :>> ', error);
        const message = error.response?.data?.message;

        if (error.response?.status === 422) {
            const validationErrors = normalizeBackendErrors(error.response.data?.errors || {});
            setErrors(validationErrors);
            
            const firstField = Object.keys(validationErrors)[0];
            
            const input = document.querySelector(`[name="${firstField}"]`) as HTMLInputElement | null;
            if (input) input.focus();
        } else {
            showSuccessAlert(message);

            console.log('message :>> ', message);
        }
    } finally {
        isLoading.value = false;
    }
});

const toggleEdit = () => {
    canEdit.value = !canEdit.value;
}
</script>

<style scoped>
</style>
