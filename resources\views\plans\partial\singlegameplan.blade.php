<ul class="no-list selected-plan-list">
    @foreach ($questions as $question)
        <li>
            @if (
                ($question->question_key === 'courses' && $gameplan->all_courses->isNotEmpty()) ||
                    $question->question_key != 'courses')
                <div>{{ $question->text }}</div>
            @endif

            <div class="plan-answer">
                @if ($question->question_key === 'finishing_school')
                    {{ @$gameplan->finishingSchool->value }}
                @elseif ($question->question_key === 'interested_in')
                    @foreach ($gameplan->interestedIn as $interest)
                        <span class="label label-dark">{{ $interest->value }}</span>
                    @endforeach
                @elseif ($question->question_key === 'industries')
                   @if (!empty($gameplan->industries) && $gameplan->industries->isNotEmpty())
                        @foreach ($gameplan->industries as $industry)
                            <span class="label label-dark">{{ $industry->name }}</span>
                        @endforeach
                    @else
                        N/A
                    @endif
                @elseif ($question->question_key === 'jobs')
                    @forelse ($gameplan->jobs as $job)
                        <span class="label label-dark">{{ $job->job_title }}</span>
                    @empty
                    @endforelse
                    @forelse ($user->userSelectedOccupations as $occupation)
                        <span class="label label-dark">{{ $occupation->anzsco_title }}</span>
                    @empty
                    @endforelse
                    @if ($gameplan->jobs->isEmpty() && $user->userSelectedOccupations->isEmpty())
                     NA
                    @endif
                @elseif ($question->question_key === 'courses' && $gameplan->all_courses->isNotEmpty())
                    @forelse ($gameplan->all_courses as $course)
                        <span
                            class="label label-dark">{{ isset($course->name) ? $course->name : $course->other_course }}</span>
                    @empty
                        NA
                    @endforelse
                @elseif ($question->question_key === 'institutes')
                    @forelse ($gameplan->institutes as $institute)
                        @if ($institute->instituteable)
                            <span class="label label-dark">{{ $institute->instituteable->name }}</span>
                        @elseif ($institute->other_institute)
                            <span class="label label-dark">{{ $institute->other_institute }}</span>
                        @endif
                    @empty
                        NA
                    @endforelse
                @elseif ($question->question_key === 'companies')
                    @forelse ($gameplan->allCompanies as $company)
                        <span class="label label-dark">{{ $company->company_name ?? $company->name }}</span>
                    @empty
                        NA
                    @endforelse
                @elseif ($question->question_key === 'anything_else')
                    {{ $gameplan->anything_else ?? 'NA' }}
                @elseif ($question->type->value === 'checkbox' || $question->type->value === 'radio')
                    @php
                        $answers = $gameplan->questionAnswers->where('gameplan_question_id', $question->id);
                    @endphp
                    @foreach ($answers as $answer)
                        <span class="label label-dark">{{ $answer->option->value }}</span>
                    @endforeach
                @endif
            </div>
        </li>
    @endforeach
</ul>
