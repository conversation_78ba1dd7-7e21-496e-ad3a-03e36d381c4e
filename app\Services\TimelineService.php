<?php

namespace App\Services;

use App\ActivityLog;
use App\Company;
use App\Gameplan;
use App\GameplanCompany;
use App\GameplanInstitute;
use App\GameplanQuestion;
use App\GameplanQuestionOption;
use App\Lessonresponse;
use App\NonStudentPlan;
use App\Plan;
use App\SkillstrainingResponse;
use App\StudentModuleResult;
use App\StudentTemplateProgress;
use App\WorkexperienceResponse;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Support\Facades\Log;

class TimelineService
{
    /**
     * List of model class names which support this service
     *
     * @param boolean $onlyFullySupported
     * @return array
     */
    public function getSupportedModelsList($onlyFullySupported = true)
    {
        $models = [
            'App\StudentTemplateProgress',
            'App\StudentModuleResult',
            'App\Lessonresponse',
            'App\WorkexperienceResponse',
            'App\SkillstrainingResponse',
            'App\Cv',
            'App\Setting',
            'App\ContactPageModel',
            'App\HomePageModel',
        ];

        if (!$onlyFullySupported) {
            $models[] = 'App/Plan';
            $models[] = 'App/NonStudentPlan';
            $models[] = 'App/Gameplan';
            $models[] = 'App\GameplanCompany';
        }

        return $models;
    }

    /**
     * Create new timeline log
     *
     * @param mixed $model
     * @param string $event
     * @param string $description
     * @param array $properties
     * @return void
     */
    public function log($model, $event = 'created', $description = '', $properties = [])
    {
        if (in_array($description, [null, ''])) {
            $description = $this->getLogDescriptionByModel($model, $event);

            if (!$description) {
                $description = $event;
            }
        }

        $properties = array_merge($properties, $this->getPropertiesByModel($model, $event));


        $singleEntryModels = [
            'App\Lessonresponse',
            'App\SkillstrainingResponse',
            'App\WorkexperienceResponse',
        ];

        if (in_array($model::class, $singleEntryModels)) {
            Activity::updateOrCreate(
                [
                    'subject_type' => get_class($model),
                    'subject_id' => $model->id,
                    'causer_id' => Auth::id(),
                    'causer_type' => 'App\User',
                ],
                [
                    'log_name' => 'timeline',
                    'description' => $description,
                    'event' => $event,
                    'properties' => $properties,
                ]
            );
        } else {
            activity('timeline')
                ->performedOn($model)
                ->event($event)
                ->withProperties($properties)
                ->log($description);
        }
    }
    /**
     * Get discription of timeline log according to model class
     *
     * @param mixed $model
     * @param string $event
     * @return mixed
     */
    public function getLogDescriptionByModel($model, $event = 'created')
    {
        switch ($model::class) {
            case 'App\StudentTemplateProgress':  // Industry content
                return $this->getStudentTemplateProgressLogDescription($model);
                break;

            case 'App\StudentModuleResult':  // Profiling
                return $this->getStudentModuleResultLogDescription($model);
                break;

            case 'App\Lessonresponse':
                return $this->getLessonResponseLogDescription($model);
                break;

            case 'App\WorkexperienceResponse':
                return $this->getWorkexperienceResponseLogDescription($model);
                break;

            case 'App\SkillstrainingResponse':
                return $this->getSkillstrainingResponseLogDescription($model);
                break;

            case 'App\Cv':  // Resume
                return $this->getCvLogDescription($model);
                break;

            case 'App\Setting': // For Portfolio Settings
                return $this->getPortfolioSettingLogDescription($model, $event);
                break;

            case 'App\GameplanCompany':   
                return $this->getGameplanCompanyLogDescription($model, $event);
                break;

            default:
                return null;
                break;
        }
    }

    public function getPropertiesByModel($model, $event = 'created')
    {
        $properties = [];

        // Handling event-based attribute storage (old/new data on update or just data on create)
        if ($event == 'updated') {
            $properties = [
                "attributes" => [
                    "old" => $model->getRawOriginal(),
                    "new" => $model->getAttributes(),
                ],
            ];
        } else {
            $properties = [
                "attributes" => [
                    "data" => $model->getAttributes(),
                ],
            ];
        }

        // Switch case based on the model class
        switch ($model::class) {
            case Lessonresponse::class:  // staus modelname coln_name fr_id percetage
                $model = $model->load('lesson:id,title');
                $lesson = $model->lesson;
                $completedPercent = $lesson->userProgress($model->student_id);
                // $status = ($model->status === 'Draft') ? 'commenced' : $model->status;
                $properties = array_merge($properties, [
                    'status' => $completedPercent == 100 ? 'completed' : 'commenced',
                    'model_name' => 'App\Lesson',
                    'column_name' => 'lesson_id',
                    'foreign_id' => $lesson->id ?? null,
                    'completed_percentage' => $completedPercent,
                ]);
                break;

            case WorkexperienceResponse::class: // staus modelname coln_name fr_id percetage
                $model = $model->load('template:id,title');
                $workexperience = $model->template;
                $completedPercent = $workexperience->userProgress($model->student_id);
                // $status = ($model->status === 'Draft') ? 'commenced' : $model->status;
                $properties = array_merge($properties, [
                    'status' => $completedPercent == 100 ? 'completed' : 'commenced',
                    'model_name' => 'App\WorkexperienceTemplate',
                    'column_name' => 'template_id',
                    'foreign_id' => $workexperience->id,
                    'completed_percentage' =>  $completedPercent,
                ]);
                break;

            case SkillstrainingResponse::class: // staus modelname coln_name fr_id percetage
                $model = $model->load('template:id,title');
                $skillstraining = $model->template;
                $completedPercent = $skillstraining->userProgress($model->student_id);
                // $status = ($model->status === 'Draft') ? 'commenced' : $model->status;
                $properties = array_merge($properties, [
                    'status' => $completedPercent == 100 ? 'completed' : 'commenced',
                    'model_name' => 'App\SkillstrainingTemplate',
                    'column_name' => 'template_id',
                    'foreign_id' => $skillstraining->id,
                    'completed_percentage' => $completedPercent,
                ]);
                break;

            case StudentTemplateProgress::class: //modelname coln_name fr_id
                $model = $model->load('template:id,title');
                $template = $model->template;
                $properties = array_merge($properties, [
                    'model_name' => 'App\Industryunit',
                    'column_name' => 'template_id',
                    'foreign_id' => $template->id,
                ]);
                break;

            default:
                break;
        }

        return $properties;
    }



    public function getLessonResponseLogDescription($model, $dateTime = null)
    {
        if (!$dateTime) {
            $dateTime = now()->format('M d, Y g:i a');
        } else {
            $dateTime = Carbon::parse($dateTime)->format('M d, Y g:i a');
        }

        $model->refresh();

        $model = $model->load('lesson:id,title', 'student:id,name');

        $lesson = $model->lesson;
        $completedPercent = $lesson->compeletedpercent;
        $lessonLink = "<a href='/#/tasks/lessons/" . ($lesson?->id ?? "") . "' target='_blank'>" . $lesson?->title . "</a>";

        // $studentView = "<p>You completed the <a href='/#/tasks/lessons/" . ($model->lesson?->id ?? "") ."'>" . $model->lesson?->title . "</a> lesson.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";
        // $adminView = "<p>" . $model->student?->name . " completed the <a href='/#/tasks/lessons/" . ($model->lesson?->id ?? "") ."'>" . $model->lesson?->title . "</a> lesson.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";

        if ($model->status == "Submitted") {
            $html = "completed $lessonLink. <a href='/#/tasks/lessons/" . ($model->lesson?->id ?? "") . "/response' target='_blank'>See response.</a>";
        } else {
            $html = "commenced the $lessonLink: " . $completedPercent . "% completed.";
        }

        return json_encode(compact('html'));
    }


    public function getStudentTemplateProgressLogDescription($model, $dateTime = null)
    {
        if (!$dateTime) {
            $dateTime = Carbon::parse($model->created_at)->format('M d, Y g:i a');
        } else {
            $dateTime = Carbon::parse($dateTime)->format('M d, Y g:i a');
        }

        $model = $model->load('template:id,title', 'student:id,name');

        // $studentView = "<p>You completed the <a href='/exploreindustries/" . ($model->template?->industries?->first()?->id ?? "") . "/unit/" . $model->template?->id . "'>" . $model->template?->title . "</a> unit.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";
        // $adminView = "<p>" . $model->student?->name . " completed the <a href='/exploreindustries/" . ($model->template?->industries?->first()?->id ?? "") . "/unit/" . $model->template?->id . "'>" . $model->template?->title . "</a> unit.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";

        $html = "looked at <a href='/exploreindustries/" . ($model->template?->industries?->first()?->id ?? "") . "/unit/" . $model->template?->id . "' target='_blank'>" . $model->template?->title . "</a> unit.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";


        return json_encode(compact('html'));
    }

    public function getStudentModuleResultLogDescription($model, $dateTime = null)
    {
        if (!$dateTime) {
            $dateTime = Carbon::parse($model->created_at)->format('M d, Y g:i a');
        } else {
            $dateTime = Carbon::parse($dateTime)->format('M d, Y g:i a');
        }

        $model = $model->load('student:id,name');

        // $studentView = '<p>You completed your <a href="/profiler/result">profiling</a>.</p><div class="event-date"> <small class="fs-12 hint-text">' . $dateTime . '</small></div>';
        // $adminView = '<p>' . $model->student->name . ' completed their <a href="/students/' . $model->user_id . '/profiling">profiling</a>.</p><div class="event-date"> <small class="fs-12 hint-text">' . $dateTime . '</small></div>';

        $html = 'completed <a href="/profiler/result" target="_blank">profiling</a>.</p><div class="event-date"> <small class="fs-12 hint-text">' . $dateTime . '</small></div>';

        return json_encode(compact('html'));
    }

    public function getWorkexperienceResponseLogDescription($model, $dateTime = null)
    {
        if (!$dateTime) {
            $dateTime = now()->format('M d, Y g:i a');
        } else {
            $dateTime = Carbon::parse($dateTime)->format('M d, Y g:i a');
        }

        $model = $model->load('template:id,title', 'student:id,name');
        $completedPercent = $model->template->compeletedpercent ?? 0;

        // $studentView = "<p>You completed the <a href='/#/tasks/vwe/" . ($model->template?->id ?? "") ."'>" . $model->template?->title . "</a> virtual work experience.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";
        // $adminView = "<p>" . $model->student?->name . " completed the <a href='/#/tasks/vwe/" . ($model->template->id ?? "") ."'>" . $model->template?->title . "</a>  virtual work experience.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";

        if ($model->status == "Submitted") {
            $html = "completed <a href='/#/tasks/vwe/" . ($model->template?->id ?? "") . "' target='_blank'>" . $model->template?->title . "</a> virtual work experience.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";
        } else {
            $html = "commenced the <a href='/#/tasks/vwe/" . ($model->template?->id ?? "") . "' target='_blank'>" . $model->template?->title . "</a> virtual work experience: " . $completedPercent . "% completed.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";
        }

        return json_encode(compact('html'));
    }

    public function getSkillstrainingResponseLogDescription($model, $dateTime = null)
    {
        if (!$dateTime) {
            $dateTime = now()->format('M d, Y g:i a');
        } else {
            $dateTime = Carbon::parse($dateTime)->format('M d, Y g:i a');
        }
        // $model->refresh();

        $model = $model->load('template:id,title', 'student:id,name');
        $completedPercent = $model->template->compeletedpercent ?? 0;

        // $studentView = "<p>You completed the <a href='/#/tasks/skillstraining/" . ($model->template->id ?? "") ."'>" . $model->template->title . "</a> skill training.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";
        // $adminView = "<p>" . $model->student?->name . " completed the <a href='/#/tasks/skillstraining/" . ($model->template->id ?? "") ."'>" . $model->template->title . "</a>  skill training.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";

        if ($model->status == "Submitted") {
            $html = "completed <a href='/#/tasks/skillstraining/" . ($model->template->id ?? "") . "' target='_blank'>" . $model->template->title . "</a> skills training.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div> <div> <a href='/#/tasks/skillstraining/" . ($model->template?->id ?? "") . "'>See  response.</a> </div>";
        } else {
            $html = "commenced the <a href='/#/tasks/skillstraining/" . ($model->template->id ?? "") . "' target='_blank'>" . $model->template->title . "</a> skills training. " . $completedPercent . "% completed.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";
        }

        return json_encode(compact('html'));
    }

    public function getCvLogDescription($model, $dateTime = null)
    {
        if (!$dateTime) {
            $dateTime = now()->format('M d, Y g:i a');
        } else {
            $dateTime = Carbon::parse($dateTime)->format('M d, Y g:i a');
        }

        $model = $model->load('user:id,name');

        // $studentView = "<p>You created a <a href='/cvs/" . ($model->id ?? "") ."/pdf'>Resume " . ($model->name ? "(".$model->name.")" : "") ."</a>.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";
        // $adminView = "<p>" . $model->user?->name . " created a <a href='/cvs/" . ($model->id ?? "") ."/pdf'>Resume " . ($model->name ? "(".$model->name.")" : "") ."</a>.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";

        $html = "created a <a href='/cvs/" . ($model->id ?? "") . "/pdf' target='_blank'>Resume " . ($model->name ? "(" . $model->name . ")" : "") . "</a>.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";

        return json_encode(compact('html'));
    }


    public function getPortfolioSettingLogDescription($model, $event, $dateTime = null)
    {
        if (!$dateTime) {
            $dateTime = now()->format('M d, Y g:i a');
        } else {
            $dateTime = Carbon::parse($dateTime)->format('M d, Y g:i a');
        }

        $model = $model->load('user:id,name');

        // $studentView = "<p>You ".$event." a <a href='" . config('services.portfolio.url') . "/" . ($model->user_id ?? "") ."/". ($model->site_url) ."'>Portfolio " ."</a>.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";
        // $adminView = "<p>" . $model->user?->name . " " .$event." a <a href='" . config('services.portfolio.url') . "/"  . ($model->user_id ?? "") ."/". ($model->site_url) ."'>Portfolio " ."</a>.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";

        $html = $event . " <a href='" . config('services.portfolio.url') . "/" . ($model->user_id ?? "") . "/" . ($model->site_url) . "' target='_blank'> ePortfolio " . "</a>.</p><div class='event-date'> <small class='fs-12 hint-text'>" . $dateTime . "</small> </div>";

        return json_encode(compact('html'));
    }

    public function getGameplanCompanyLogDescription($model, $event = 'created', $dateTime = null)
    {
        $dateTime = $dateTime ? Carbon::parse($dateTime)->format('M d, Y g:i a') : now()->format('M d, Y g:i a');

        $companyId = $model->company_id;
        $companyName = $model->company_name ?? $model->company?->name;

        if (!$companyId || !$companyName) {
            return null;
        }

        $companyLink = "<a href='/#/tools/employerSearch/viewCompany/{$companyId}' target='_blank'>"
            . htmlspecialchars($companyName) . "</a>";

        // Determine Gameplan owner
        $gameplanOwnerId = $model->gameplan?->user_id ?? null;
        $isSelf = $gameplanOwnerId && Auth::id() === $gameplanOwnerId;

        if ($isSelf) {
            $text = "You added {$companyLink} to your Game Plan.";
        } else {
            $studentName = $model->gameplan?->user?->name ?? 'Student';
            $text = "{$studentName} added {$companyLink} to their Game Plan.";
        }

        $html = "<p>{$text}</p>
        <div class='event-date'>
            <small class='fs-12 hint-text'>{$dateTime}</small>
        </div>";

        return json_encode(compact('html'));
    }





    /**
     * Get Description with manipulated HTML content
     *
     * @param mixed $activity
     * @return string
     */
    public function getHtmlDescription(mixed $activity): string
    {
        $html = '';
        $description = $activity->customDescription();

        // dd($description);


        // if (in_array($activity->subject_type, ['App\Plan', 'App\NonStudentPlan' , 'App\Gameplan'])) {


        if ($activity->subject_type === 'App\Gameplan') {
            // Render full game plan view if no company addition
            $gameplanId = $activity->subject_id;
            $gameplan = Gameplan::with(['industries', 'jobs', 'interestedIn', 'institutes', 'institutes.instituteable', 'companies'])->find($gameplanId);

            if (Auth::user()->isAdmin()) {
                $user = $activity->causer;
            } else {
                $user = Auth::user();
            }

            if ($gameplan) {
                $gameplanInstitute = GameplanInstitute::where('gameplan_id', $gameplan->id)->first();
                if ($gameplanInstitute) {
                    $gameplan->institute = $gameplanInstitute->instituteable;
                }

                $instituteDetail = $user->school?->detail;
                $instituteId = $instituteDetail?->school_id;
                $instituteType = $instituteDetail?->institute_type->value ?? null;

                $questions = GameplanQuestion::with('options')
                    ->orderBy('sort_order')
                    ->when(Auth::user()->isIndividual(), function ($query) {
                        $query->where('question_key', '<>', 'finishing_school');
                    })
                    ->where(function ($query) use ($instituteType, $instituteId) {
                        $query->where('institute_type', $instituteType)
                            ->where(function ($subQuery) use ($instituteId) {
                                $subQuery->whereNull('institute_id')
                                    ->orWhere('institute_id', $instituteId);
                            })
                            ->orWhereNull('institute_type');
                    })
                    ->get();

                    $companies = $gameplan->allCompanies()->get();

                $html = view('plans.partial.singlegameplan', compact('gameplan', 'questions', 'user', 'companies'))->render();
            }
        }else if (in_array($activity->subject_type, ['App\Plan', 'App\NonStudentPlan'])) {
            $planId = $activity->subject_id;
            if (request('nonStudent') /* || (Auth::user()->isNonStudent()) */) {
                $plan = NonStudentPlan::where('id', $planId)->with(['currentIndustry', 'getintoIndustry'])->first();
                $nonStudent = true;
            } else {
                $plan = Plan::where('id', $planId)->with(['tafes', 'colleges', 'universities',  'industries'])->first();
                $nonStudent = false;
            }

            $html = view('plans.partial.singleplan', compact('plan', 'nonStudent'))->render();
        } else {
            return $description ?? '';
            if ($description != '') {

                $jsonData = json_decode($description, true);

                if (Auth::user()->isStudent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff()) && session('studentView'))) {
                    if (isset($jsonData['studentView'])) {
                        $html = $jsonData['studentView'];
                    }

                    if ($html == '' && isset($jsonData['html'])) {
                        $html = "<p> You " . $jsonData['html'];
                    }
                } else {
                    if (isset($jsonData['adminView'])) {
                        $html = $jsonData['adminView'];
                    }

                    if ($html == '' && isset($jsonData['html'])) {
                        $html = "<p>" . $activity->causer?->name . " " . $jsonData['html'];
                    }
                }
            }
        }

        return $html;
    }
}
