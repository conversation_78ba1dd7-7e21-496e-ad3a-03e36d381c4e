<template>
    <div id="kt_project_users_map_pane" class="tab-pane fade show active">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-center align-items-center" v-if="loading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div v-else>
                            <!-- Map Container -->
                            <div id="australia-map" style="width: 100%; height: 500px"></div>

                            <!-- Map Legend -->
                            <div class="mt-5">
                                <div class="d-flex justify-content-center align-items-center gap-4">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #dbdfe9"></div>
                                        <span class="fs-7 text-muted">No Employer</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #c2dffc"></div>
                                        <span class="fs-7 text-muted">1-10 Employers</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #009ef7"></div>
                                        <span class="fs-7 text-muted">11-50 Employers</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #1b84ff"></div>
                                        <span class="fs-7 text-muted">51+ Employers</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from "vue";
import * as am5 from "@amcharts/amcharts5";
import * as am5map from "@amcharts/amcharts5/map";
import am5geodata_australiaLow from "@amcharts/amcharts5-geodata/australiaLow";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";

interface Company {
    id: number;
    name: string;
    logo: string;
    location: string[];
    description: string;
    profileUrl: string;
}

export default defineComponent({
    name: "EmployerCourseMapView",
    props: {
        companies: {
            type: Array as () => Company[],
            required: true,
        },
    },
    setup(props) {
        const loading = ref(false);

        onMounted(() => {
            let root = am5.Root.new("australia-map");
            root.setThemes([am5themes_Animated.new(root)]);

            let chart = root.container.children.push(
                am5map.MapChart.new(root, {
                    panX: "none",
                    panY: "none",
                    projection: am5map.geoMercator(),
                })
            );

            let polygonSeries = chart.series.push(
                am5map.MapPolygonSeries.new(root, {
                    geoJSON: am5geodata_australiaLow,
                })
            );

            polygonSeries.mapPolygons.template.setAll({
                tooltipText: "{name}: {EmployerCount} Employers",
                interactive: true,
            });
            

            polygonSeries.mapPolygons.template.states.create("hover", {
                fill: am5.color("#141414"),
            });

            polygonSeries.events.on("datavalidated", () => {
                setMapData();
            });

            setMapData();

            function setMapData() {
                if (!polygonSeries) {
                    console.warn("polygonSeries not available, cannot set map data.");
                    return;
                }

                try {
                    const regionMapping = {
                        "New South Wales": "AU-NSW",
                        "Victoria": "AU-VIC",
                        "Queensland": "AU-QLD",
                        "South Australia": "AU-SA",
                        "Western Australia": "AU-WA",
                        "Tasmania": "AU-TAS",
                        "Northern Territory": "AU-NT",
                        "Australian Capital Territory": "AU-ACT",
                    };

                    const regionCounts: { [key: string]: number } = {};
                    props.companies.forEach((company: Company) => {
                        // Iterate over the array of locations (states) for each company
                        company.location.forEach((state: string) => {
                            const regionId = regionMapping[state];
                            if (regionId) {
                                regionCounts[regionId] = (regionCounts[regionId] || 0) + 1;
                            }
                        });
                    });

                    polygonSeries.mapPolygons.each(polygon => {
                        const regionId = polygon.dataItem.get("id");
                        const companyCount = regionCounts[regionId] || 0;

                        polygon.dataItem.set("EmployerCount", companyCount);

                        let color = "#DBDFE9"; // Default gray - no companies
                        if (companyCount > 0 && companyCount <= 10) {
                            color = "#C2DFFC"; // Light blue - 1-10 companies
                        } else if (companyCount > 10 && companyCount <= 50) {
                            color = "#009EF7"; // Medium blue - 11-50 companies
                        } else if (companyCount > 50) {
                            color = "#1B84FF"; // Dark blue - 51+ companies
                        }

                        polygon.set("fill", am5.color(color));
                    });
                } catch (error) {
                    console.error("Failed to set map data:", error);
                }
            }
        });

        return {
            loading,
        };
    },
});
</script>