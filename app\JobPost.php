<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;

class JobPost extends Model
{
    public const STATUSES = [
        'published',
        'draft'
    ];

    public const WORK_MODES = [
        'online',
        'remote',
        'in person',
        'hybrid',
    ];

    public const SENIORITY_LEVELS = [
        'intern',
        'graduate',
        'junior',
        'mid level',
        'senior',
        'director',
        'executive',
    ];

    public const EMPLOYMENT_TYPES = [
        'part-time',
        'full-time',
        'internship',
        'casual',
        'contract',
        'temporary',
    ];

    public const PAY_FREQUENCIES = [
        'weekly',
        'monthly',
        'yearly',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'additional_links' => 'array',
    ];

    public function company()
    {
        return $this->belongsTo(User::class, 'company_id');
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    protected function isExpired(): Attribute
    {
        return Attribute::get(function () {
            if (empty($this->expire_date)) {
                return false;
            }

            return Carbon::parse($this->expire_date)->isPast();
        });
    }

    protected function dynamicStatus(): Attribute
    {
        return Attribute::get(function () {
            if ($this->status === 'published' && $this->isExpired) {
                return 'expired';
            }

            return $this->status;
        });
    }
}
