<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GameplanCompany extends Model
{
    protected $table = 'gameplan_companies';
    public $timestamps = true;
    protected $fillable = ['gameplan_id', 'company_id','company_name', 'priority'];

    // Relationships
    public function gameplan()
    {
        return $this->belongsTo(Gameplan::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }
}
