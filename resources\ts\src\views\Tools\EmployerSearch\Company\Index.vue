<template>
    <div class="full-view-banner position-relative" v-if="company">
        <!-- Banner -->
        <img :src="company.detail?.banner_image_full_Path || 'https://picsum.photos/1200/400'" class="w-100"
            style="height: 40vh; object-fit: cover;" />

        <!-- Card -->
        <div class="card p-4 mx-auto position-relative z-1 mb-xxl-8" style="margin-top: -230px; max-width: 95%;">
            <div class="card-body pt-9 ms-xl-10 pb-0">
                <div class="row">
                    <!-- Logo -->
                    <div class="col-12 col-xl-2">
                        <div class="symbol symbol-100px symbol-lg-175px symbol-fixed position-relative">
                            <img :src="company.detail?.logo_full_path || 'https://picsum.photos/200'" alt="logo"
                                style="object-fit: cover;" />
                        </div>
                    </div>

                    <!-- Company Info -->
                    <div class="col-12 mt-0 mt-md-5 col-xl-10">
                        <div class="d-flex justify-content-between align-items-start flex-wrap mb-5 mb-xl-0">
                            <div class="d-flex flex-column">
                                <div class="d-flex align-items-center mb-2">
                                    <a href="#" class="text-gray-900 text-hover-primary fs-1 fw-bold me-1">
                                        {{ company.detail?.name }}
                                    </a>
                                    <a href="#"><i class="ki-outline ki-verify fs-1 text-primary"></i></a>
                                </div>

                                <!-- Location -->
                                <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
                                    <a v-if="company.detail?.suburb" href="#"
                                        class="d-flex align-items-center text-gray-700 text-hover-primary me-5 mb-2">
                                        <i class="fa-solid fa-location-dot me-2"></i>
                                        {{ company.detail.suburb }}
                                    </a>
                                    <div v-if="company.states.length >= 1" class="d-flex flex-wrap">
                                        <a v-for="states in company.states" href="#"
                                            class="d-flex align-items-center text-gray-700 text-hover-primary me-5 mb-2">
                                            <i class="bi bi-geo-alt-fill text-black me-2"></i>
                                            {{ states.name }}
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Social Links -->
                            <div class="d-flex flex-wrap gap-2">
                                <a v-for="(link, index) in company.detail?.social_links" :key="index" :href="link.url"
                                    target="_blank"
                                    class="d-flex align-items-center gap-2 px-4 py-2 rounded bg-light text-secondary text-dark text-decoration-none fs-6 fw-medium hover-bg">
                                    <span class="text-capitalize text-gray-700">{{ link.platform }}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>

                        <!-- Industries -->
                        <div class="d-flex flex-wrap flex-stack">
                            <div class="fs-5 text-gray-700 pe-8">
                                <template v-for="(industry, i) in company.industries" :key="industry.id">
                                    {{ industry.name }}
                                    <svg v-if="i < company.industries.length - 1" class="m-2" width="3" height="3"
                                        viewBox="0 0 3 3" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="1.5" cy="1.5" r="1.5" fill="#606060" />
                                    </svg>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <div class="row">
                <div class="col-md-5 col-xl-2 ms-xl-18">
                    <div class="card">
                        <div class="card-body p-0">
                            <ul
                                class="nav nav-tabs nav-pills flex-row border-0 flex-md-column me-xl-5 mb-3 mb-md-0 fs-6 mt-3">
                                <li v-for="tab in tabs" :key="tab.key" class="nav-item w-100 me-0 mb-md-2">
                                    <!-- Main Tab with Toggle -->
                                    <button
                                        class="nav-link py-5 w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark d-flex justify-content-between align-items-center"
                                        type="button" v-if="tab.children && tab.children.length"
                                        data-bs-toggle="collapse" :data-bs-target="`#collapse-${tab.key}`"
                                        :aria-expanded="isExpanded(tab.key)" :aria-controls="`collapse-${tab.key}`"
                                        :class="{ active: activeTab === tab.key }"
                                        @click="toggleExpanded(tab.key); setActiveTab(tab)">
                                        <span class="fs-4 fw-bold">{{ tab.label }}</span>
                                        <i class="fa-solid" :class="{
                                            'fa-chevron-down': !isExpanded(tab.key),
                                            'fa-chevron-up': isExpanded(tab.key),
                                            'text-dark': activeTab === tab.key
                                        }"></i>
                                    </button>

                                    <!-- Simple Tab (no children) -->
                                    <button v-else
                                        class="nav-link py-5 w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark"
                                        type="button" :class="{ active: activeTab === tab.key }"
                                        @click="setActiveTab(tab)">
                                        <span class="fs-4 fw-bold">{{ tab.label }}</span>
                                    </button>

                                    <!-- Submenu (Pages and Subpages) -->
                                    <div v-if="tab.children && tab.children.length" :id="`collapse-${tab.key}`"
                                        class="collapse ms-4" :class="{ show: isExpanded(tab.key) }">
                                        <ul class="list-unstyled">
                                            <li v-for="child in tab.children" :key="child.key">
                                                <!-- Page with Subpages -->
                                                <button v-if="child.children && child.children.length"
                                                    class="btn btn-link nav-link text-start mx-5 d-flex justify-content-between align-items-center"
                                                    type="button" data-bs-toggle="collapse"
                                                    :data-bs-target="`#collapse-${child.key}`"
                                                    :aria-expanded="isExpanded(child.key)"
                                                    :aria-controls="`collapse-${child.key}`"
                                                    :class="{ active: activeTab === child.key }"
                                                    @click="toggleExpanded(child.key); setActiveTab(child)">
                                                    <span>
                                                        <svg class="m-2" width="3" height="3" viewBox="0 0 3 3"
                                                            fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <circle cx="1.5" cy="1.5" r="1.5" fill="#606060" />
                                                        </svg>
                                                        {{ child.label }}
                                                    </span>
                                                    <i class="fa-solid mx-2" :class="{
                                                        'fa-chevron-down': !isExpanded(child.key),
                                                        'fa-chevron-up': isExpanded(child.key),
                                                        'text-dark': activeTab === child.key
                                                    }"></i>
                                                </button>
                                                <!-- Simple Page (no subpages) -->
                                                <button v-else class="btn btn-link nav-link text-start mx-5"
                                                    :class="{ active: activeTab === child.key }"
                                                    @click="setActiveTab(child)">
                                                    <span>
                                                        <svg class="m-2" width="3" height="3" viewBox="0 0 3 3"
                                                            fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <circle cx="1.5" cy="1.5" r="1.5" fill="#606060" />
                                                        </svg>
                                                        {{ child.label }}
                                                    </span>
                                                </button>

                                                <!-- Subpages -->
                                                <div v-if="child.children && child.children.length"
                                                    :id="`collapse-${child.key}`" class="collapse ms-8"
                                                    :class="{ show: isExpanded(child.key) }">
                                                    <ul class="list-unstyled">
                                                        <li v-for="subchild in child.children" :key="subchild.key">
                                                            <!-- Subpage with Sub-subpages -->
                                                            <button v-if="subchild.children && subchild.children.length"
                                                                class="btn btn-link nav-link text-start mx-5 d-flex justify-content-between align-items-center"
                                                                type="button" data-bs-toggle="collapse"
                                                                :data-bs-target="`#collapse-${subchild.key}`"
                                                                :aria-expanded="isExpanded(subchild.key)"
                                                                :aria-controls="`collapse-${subchild.key}`"
                                                                :class="{ active: activeTab === subchild.key }"
                                                                @click="toggleExpanded(subchild.key); setActiveTab(subchild)">
                                                                <span>
                                                                    <svg class="m-2" width="3" height="3"
                                                                        viewBox="0 0 3 3" fill="none"
                                                                        xmlns="http://www.w3.org/2000/svg">
                                                                        <circle cx="1.5" cy="1.5" r="1.5"
                                                                            fill="#606060" />
                                                                    </svg>
                                                                    {{ subchild.label }}
                                                                </span>
                                                                <i class="fa-solid mx-2" :class="{
                                                                    'fa-chevron-down': !isExpanded(subchild.key),
                                                                    'fa-chevron-up': isExpanded(subchild.key),
                                                                    'text-dark': activeTab === subchild.key 
                                                                }"></i>
                                                            </button>

                                                            <!-- Simple Subpage (no sub-subpages) -->
                                                            <button v-else class="btn btn-link nav-link text-start mx-5"
                                                                :class="{ active: activeTab === subchild.key }"
                                                                @click="setActiveTab(subchild)">
                                                                <span>
                                                                    <svg class="m-2" width="3" height="3"
                                                                        viewBox="0 0 3 3" fill="none"
                                                                        xmlns="http://www.w3.org/2000/svg">
                                                                        <circle cx="1.5" cy="1.5" r="1.5"
                                                                            fill="#606060" />
                                                                    </svg>
                                                                    {{ subchild.label }}
                                                                </span>
                                                            </button>

                                                            <!-- Sub-subpages (Level 4) -->
                                                            <div v-if="subchild.children && subchild.children.length"
                                                                :id="`collapse-${subchild.key}`" class="collapse ms-12"
                                                                :class="{ show: isExpanded(subchild.key) }">
                                                                <ul class="list-unstyled">
                                                                    <li v-for="subsubchild in subchild.children"
                                                                        :key="subsubchild.key">
                                                                        <button
                                                                            class="btn btn-link nav-link text-start mx-5"
                                                                            :class="{ active: activeTab === subsubchild.key }"
                                                                            @click="setActiveTab(subsubchild)">
                                                                            <span>
                                                                                <svg class="m-2" width="3" height="3"
                                                                                    viewBox="0 0 3 3" fill="none"
                                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                                    <circle cx="1.5" cy="1.5" r="1.5"
                                                                                        fill="#606060" />
                                                                                </svg>
                                                                                {{ subsubchild.label }}
                                                                            </span>
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-7 col-xl-9 px-0">
                    <component :is="currentComponent" v-bind="currentProps" @page-change="handlePageChange"
                        @search="handleSearch" />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { defineComponent, ref, onMounted, computed, watch } from 'vue';
    import { useRoute } from "vue-router";
    import BlockView from "@/components/employer/search/BlockView.vue";
    import ListView from "@/components/employer/search/ListView.vue";
    import MapView from "@/components/employer/search/MapView.vue";
    import TablePagination from "@/components/kt-datatable/table-partials/table-content/table-footer/TablePagination.vue";
    import axios from "axios";
    import About from "./About.vue";
    import Jobs from "./Jobs.vue"
    import Life from "./Life.vue"
    import LinkedContent from "./LinkedContent.vue"
    import LinkedCourses from "./LinkedCourses.vue"
    import DefaultComponent from "./DefaultComponent.vue"
    import {
        Field
    } from "vee-validate";
    import Multiselect from '@vueform/multiselect';

    export default defineComponent({
        components: {
            BlockView,
            ListView,
            MapView,
            TablePagination,
            Field,
            Multiselect,
            About,
            Jobs,
            Life,
            LinkedContent,
            LinkedCourses,
            DefaultComponent
        },


        setup() {
            const activeTab = ref("about");
            const route = useRoute();
            const company = ref<any>(null);
            const loading = ref(false);
            const tabs = ref<any[]>([]);
            const expandedTabs = ref<Set<string>>(new Set());
            const banner = ref({
                'trailer_video': null,
                'video': null,
                'imagefullpath': null,
            });
            const jobs = ref<any>({ data:null, meta:null});
            const linkedContent = ref<any>({ data: null, meta: null });
            const linkedCourses = ref<any>({ data: null, meta: null });

            const currentProps = computed(() => {
                switch (activeTab.value) {
                    case 'about':
                         return { company: company.value };
                    case 'jobs':
                        return { jobs: jobs.value };
                    case 'linkedContent':
                        return { linkedContent: linkedContent.value };
                    case 'linkedCourses':
                        return { linkedCourses: linkedCourses.value };
                    default:
                        return company.value?.pages?.find((page) => `page-${page.id}` === activeTab.value)
                            ? { page: company.value.pages.find((page) => `page-${page.id}` === activeTab.value) }
                            : {};
                }
            });
            
            const currentComponent = computed(() => {
            switch (activeTab.value) {
                case "about":
                return "About";
                case "jobs":
                return "Jobs";
                case "life":
                return "Life";
                case "linkedContent":
                return "LinkedContent";
                case "linkedCourses":
                return "LinkedCourses";
                default:
                return company.value?.pages?.find((page) => `page-${page.id}` === activeTab.value)
                            ? Life
                            : DefaultComponent;
                }
            });
          
            const staticTabs = [
                { key: "about", label: "About" },
                { key: "jobs", label: "Jobs & Opportunities" },
                { key: "linkedContent", label: "Linked Content" },
                { key: "linkedCourses", label: "Linked Courses" },
            ];

            // const fetchBanner = async () => {
            //     try {
            //         const response = await fetch(
            //             'api/getBanner/Employer Pipeline',
            //             {
            //                 method: 'GET',
            //                 headers: {
            //                     'Content-Type': 'application/json',
            //                     'X-Requested-With': 'XMLHttpRequest',
            //                 },
            //             }
            //         );

            //         const data = await response.json();
            //         banner.value = data;
            //     } catch (error) {
            //         console.log(error)
            //     }
            // };
           
            const fetchCompany = async () => {
                loading.value = true;
                try {
                    const response = await axios.get(`/api/companies/${route.params.id}/fetchCompanydata`);
                    company.value = response.data.data;

                    // Recursive function to build tab hierarchy
                    const buildTabHierarchy = (pages: any[], parentId: number | null) => {
                        return pages
                            .filter((page: any) => page.parent_id === parentId)
                            .map((page: any) => ({
                                key: `page-${page.id}`,
                                label: page.title,
                                component: Life,
                                props: { page },
                                children: buildTabHierarchy(pages, page.id), // Recursively build children
                            }));
                    };

                    // Build dynamic tabs from pages
                    const dynamicTabs = buildTabHierarchy(company.value.pages, null);

                    // Merge static and dynamic tabs
                    tabs.value = [
                        ...staticTabs.slice(0, 2).map((tab) => ({
                            ...tab,
                            props: { company: company.value },
                        })),
                        ...dynamicTabs,
                        ...staticTabs.slice(2).map((tab) => ({
                            ...tab,
                            props:
                                tab.key === 'linkedContent'
                                    ? { linkedContent: linkedContent.value }
                                    : tab.key === 'linkedCourses'
                                        ? { linkedCourses: linkedCourses.value } 
                                                 : { company: company.value },
                        })),
                    ];
                } catch (error) {
                    console.error('Error fetching company:', error);
                } finally {
                    loading.value = false;
                }
            };

            const fetchLinkedContent = async (page: number = 1, search: string = '') => {
                loading.value = true;
                try {
                    const response = await axios.get(`/api/companies/${route.params.id}/linked-content`, {
                        params: { page, search },
                    });
                    linkedContent.value = {
                        data: response.data.data,
                        meta: response.data.meta,
                    };
                } catch (error) {
                    console.error('Error fetching linked content:', error);
                    linkedContent.value = { data: [], meta: { total: 0, current_page: 1, per_page: 4, last_page: 1, from: 1, to: 4 } };
                } finally {
                    loading.value = false;
                }
            };

            const fetchLinkedCourses = async (page: number = 1, search: string = '') => {
                try {
                    const response = await axios.get(`/api/companies/${route.params.id}/linked-courses`,{ params: {page, search}});
                    linkedCourses.value = {
                        data: response.data.data,
                        meta: response.data.meta,
                    }
                } catch (error) {
                    console.error('Error fetching linked courses:', error);
                    linkedCourses.value = { data: [], meta: { total: 0, current_page: 1, per_page: 4, last_page: 1, from: 1, to: 4 } };
                } finally {
                    loading.value = false;
                }
            }; 

            const fetchLinkedJobs = async (
                page: number = 1,
                searchQuery: string = '',
                filters: { location?: string | null; courseType?: string | null } = {}
            ) => {
                try {
                    const response = await axios.get(`/api/companies/${route.params.id}/jobs_opportunities`, {
                        params: {
                            page,
                            searchQuery,  
                            ...filters   
                        }
                    });

                    jobs.value = {
                        data: response.data.data,
                        meta: response.data.meta,
                    }
                } catch (error) {
                    console.error('Error fetching linked courses:', error);
                    jobs.value = { data: [], meta: { total: 0, current_page: 1, per_page: 4, last_page: 1, from: 1, to: 4 } };
                } finally {
                    loading.value = false;
                }
            };


            const handlePageChange = (page: number) => {
                if (activeTab.value === 'linkedContent') {
                    fetchLinkedContent(page);
                }
                if(activeTab.value === 'linkedCourses'){
                    fetchLinkedCourses(page);
                }
                if(activeTab.value === 'jobs'){
                    fetchLinkedJobs(page);
                }
            };

            const handleSearch = (query: string | { searchQuery: string; filters: any }) => {
                if (activeTab.value === 'linkedContent') {
                    const search = typeof query === 'string' ? query : query.searchQuery;
                    fetchLinkedContent(1, search);
                }

                if (activeTab.value === 'linkedCourses') {
                    const search = typeof query === 'string' ? query : query.searchQuery;
                    fetchLinkedCourses(1, search);
                }

                if (activeTab.value === 'jobs') {
                    const search = typeof query === 'string' ? query : query.searchQuery;
                    const filters = typeof query === 'string' ? {} : query.filters;
                    fetchLinkedJobs(1, search, filters);
                }
            };



            const setActiveTab = async (tab: any) => {
                if (activeTab.value !== tab.key) {
                    activeTab.value = tab.key;
                    if (tab.key === 'linkedContent') {
                        await fetchLinkedContent();
                    } else if (tab.key === 'linkedCourses') {
                        await fetchLinkedCourses();
                    } else if (tab.key === 'jobs'){
                        await fetchLinkedJobs();
                    }
                }
            };

            const addExpanded = (key: string) => {
                expandedTabs.value = new Set([...expandedTabs.value, key]);
            };

            const removeExpanded = (key: string) => {
                const newSet = new Set(expandedTabs.value);
                newSet.delete(key);
                expandedTabs.value = newSet;
            };

            const isExpanded = (key: string) => expandedTabs.value.has(key);

            const toggleExpanded = (key: string) => {
                if (expandedTabs.value.has(key)) {
                    removeExpanded(key);
                } else {
                    addExpanded(key);
                }
            };
            onMounted(() => {
                // fetchBanner();
                fetchCompany();
                expandedTabs.value.add("page-1");
            });

            return {
                banner,
                loading,
                activeTab, 
                currentComponent,
                tabs,
                setActiveTab,
                company,
                isExpanded,
                addExpanded,
                removeExpanded,
                toggleExpanded,
                currentProps,
                handlePageChange,
                handleSearch
            }
        },
    })
</script>

<style>
    .banner {
        background-color: #bbb;
        display: block;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;
        overflow: hidden;
        min-height: 30vw;
    }

    .full-view-banner {
        margin-left: -30px;
        margin-right: -30px;
    }
    
    .app-content {
        padding: 0px;
    }

    .banner-video {
        height: 100%;
    }

    .banner-video>video {
        width: 101% !important;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }


    .nav-tabs .nav-link {
        border: none !important;
        color: #A1A5B7;
        background-color: transparent !important;
    }

    .nav-tabs .nav-link:hover {
        color: #000;
        background-color: transparent !important;
    }

    .nav-tabs .nav-link.active {
        color: #000;
        font-weight: bold;
        /* border-bottom: 2px solid #000 !important; */
        background-color: transparent !important;
        box-shadow: none !important;
    }

    .btn-white-custom {
        background: #fff;
        color: #000;
    }

    .btn-border-custom {
        border: 1px solid white !important;
        color: #000;
        background: white;
    }

    .btn-border-custom:hover {
        border: 0px !important;
        color: white;
        background: #000;
    }

    .btn-black-custom:hover,
    .btn-white-custom {
        background-color: #fff !important;
        color: #000 !important;
    }

    .btn-black-custom,
    .btn-white-custom:hover {
        background-color: #000 !important;
        color: #fff !important;
    }

    .btn-white-custom:hover,
    .btn.btn-white-custom:hover:not(.btn-active) {
        background-color: #000 !important;
        color: #fff !important;
    }
    @media (max-width: 1280px) {
        .banner {
            height: 95.25vw;

        }

        .banner_detail_box {
            left: 40%;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(65vw + 65vh) !important;
        }
    }


    @media (max-width: 991px) {

        .banner {
            height: 90.25vw;
        }


        .full-view-banner,
        .module-sections {
            margin-left: -20px;
            margin-right: -20px;
        }

        .full-view-banner {
            margin-top: 58.16px;
        }

    }



    @media (max-width: 991px) and (min-width: 768px) and (orientation:portrait) {
        .banner {
            height: 95.25vw;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(66vw + 66vh) !important;
        }
    }

    @media (max-width: 991px) and (orientation:landscape) {
        .banner-video>video {
            height: auto !important;
            width: calc(70vw + 70vh) !important;
        }
    }

    @media (max-width: 767px) {

        .banner {
            height: 110vw;
        }

        .banner_detail_box {
            left: 50%;
        }

    }

    @media (max-width: 575px) {
        .full-view-banner {
            margin-top: 0;
        }

        .banner {
            height: 124vw;
        }

        .banner_detail_box {
            width: 70vw !important;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(90vw + 90vh) !important;
        }
    }
</style>
