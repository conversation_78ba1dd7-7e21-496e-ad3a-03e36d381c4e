<?php

namespace App;

// use Illuminate\Database\Eloquent\Model;

use Auth;
use App\Student;
use App\SkillstrainingTemplate;
use App\WorkexperienceTemplate;
use App\Lesson;
use App\Plan;
use App\Cv;
use App\Services\TimelineService;
use App\StudentModuleResult;
use Spatie\Activitylog\Models\Activity;

class ActivityLog extends Activity
{
    public function customDescription()
    {
        $properties = json_decode($this->properties);

        if (!$properties && !$this->subject_type) {
            return null;
        }

        $responseLink = '';
        $causer = $this->causer;

        $currentUser = auth()->user();
        $studentName = $this->causer->profile->firstname;
        $isare = 'is';
        $studentResponse = "students/{$causer->id}";
        if ($causer->id == $currentUser->id) {
            $studentName = 'You';
            $isare = 'are';
            $studentResponse = 'response';
        }

        // For employer dashboard, show different descriptions
        if ($currentUser && $currentUser->role && $currentUser->role->name === 'Employer') {
            return $this->getEmployerDescription($properties, $causer);
        }


        if ($this->subject_type == "App\Lessonresponse") {
            if (property_exists($properties, 'foreign_id')) {
                $lesson = Lesson::find($properties->foreign_id);
                if ($lesson) {
                    $moduleLink = "<a href='/#/tasks/lessons/{$lesson->id}' target='_blank'>{$lesson->title}</a>";
                    if ($currentUser->isStudent()) {
                        # code...
                    }
                    $responseLink = "<a href='/#/tasks/lessons/{$lesson->id}/{$studentResponse}' target='_blank'>See response.</a>";
                    if ($properties->status === "completed") {
                        return "Module completed: {$studentName} completed {$moduleLink} Lesson. {$responseLink}";
                    } else {
                        return "Module commenced: {$studentName} {$isare} working on {$moduleLink} Lesson: {$properties->completed_percentage}% completed.";
                    }
                }
            }
        }

        if ($this->subject_type == "App\SkillstrainingResponse") {
            if (property_exists($properties, 'foreign_id')) {
                $skillstraining = SkillstrainingTemplate::find($properties->foreign_id);
                if ($skillstraining) {
                    $moduleLink = "<a href='/#/tasks/skillstraining/{$skillstraining->id}' target='_blank'>{$skillstraining->title}</a>";
                    $responseLink = "<a href='/#/tasks/skillstraining/{$skillstraining->id}/{$studentResponse}' target='_blank'>See response.</a>";

                    if ($properties->status === "completed") {
                        return "Module completed: {$studentName} completed {$moduleLink} Skills Training. {$responseLink}";
                    } else {
                        return "Module commenced: {$studentName} {$isare} working on {$moduleLink} Skills Training: {$properties->completed_percentage}% completed.";
                    }
                }
            }
        }


        if ($this->subject_type == "App\WorkexperienceResponse") {
            if (property_exists($properties, 'foreign_id')) {
                $workexperience = WorkexperienceTemplate::find($properties->foreign_id);
                if ($workexperience) {
                    $moduleLink = "<a href='/#/tasks/vwe/{$workexperience->id}' target='_blank'>{$workexperience->title}</a>";
                    $responseLink = "<a href='/#/tasks/vwe/{$workexperience->id}/{$studentResponse}' target='_blank'>See response.</a>";

                    if ($properties->status === "completed") {
                        return "Module completed: {$studentName} completed {$moduleLink} Virtual Work Experience. {$responseLink}";
                    } else {
                        return "Module commenced: {$studentName} {$isare} working on {$moduleLink} Virtual Work Experience: {$properties->completed_percentage}% completed.";
                    }
                }
            }
        }

        if ($this->subject_type == "App\StudentTemplateProgress") {
            if (property_exists($properties, 'foreign_id')) {
                $industryUnit = Industryunit::with('industries')->find($properties->foreign_id);
                $industryId = $industryUnit->industries->first()->id;
                if ($industryUnit) {
                    $moduleLink = "<a href='/#/exploreindustry/{$industryId}/template/{$industryUnit->id}' target='_blank'>{$industryUnit->title}</a>";
                    return "{$studentName} looked at {$moduleLink}.";
                }
            }
        }

        if ($this->subject_type == "App\Cv") {
            $cv = $this->subject;
            if ($cv) {
                $moduleLink = "<a href='/cvs/{$cv->id}/pdf' target='_blank'>Resume</a>";

                return "{$studentName} created a {$moduleLink}.";
            }
        }

        if ($this->subject_type == "App\Setting") {
            $portfolio = $this->subject;
            if ($portfolio) {
                $moduleLink = "<a href='" . config('services.portfolio.url') . "/" . ($portfolio->user_id ?? "") . "/" . ($portfolio->site_url) . "' target='_blank'>ePortfolio " . (@$portfolio->name) . "</a>";

                return "{$studentName} created an {$moduleLink}.";
            }
        }

        if ($this->subject_type == "App\StudentModuleResult") {
            $profiling = $this->subject;
            if ($profiling) {
                $moduleLink = "<a href='/profiler/result' target='_blank'>Profiling " . (@$profiling->name) . "</a>";
                return "{$studentName} completed the {$moduleLink}.";
            }
        }
        
        if ($this->subject_type == "App\GameplanCompany") {
            $timelineService = new TimelineService();
            $companyEntry = $this->subject;
            if ($companyEntry) {
                $htmlJson = $timelineService->getGameplanCompanyLogDescription($companyEntry, $this->event, $this->created_at);
                $htmlData = json_decode($htmlJson, true);
                return $htmlData['html'] ?? null;
            }
        }

    }

    /**
     * Get employer-specific timeline descriptions
     */
    private function getEmployerDescription($properties, $causer)
    {
        // Get student location (state name)
        $location = $this->getStudentLocation($causer);

        // Determine if student is public or private with hyperlink
        $studentIdentifier = $this->getStudentIdentifierWithLink($causer);

        if ($this->subject_type == "App\Lessonresponse") {
            if (property_exists($properties, 'foreign_id')) {
                $lesson = Lesson::find($properties->foreign_id);
                if ($lesson) {
                    $moduleLink = "<a href='/#/tasks/lessons/{$lesson->id}' target='_blank'>{$lesson->title}</a>";
                    if ($properties->status === "completed") {
                        return "{$studentIdentifier} in {$location} completed {$moduleLink} Lesson.";
                    } else {
                        return "{$studentIdentifier} in {$location} started {$moduleLink} Lesson.";
                    }
                }
            }
        }

        if ($this->subject_type == "App\SkillstrainingResponse") {
            if (property_exists($properties, 'foreign_id')) {
                $skillstraining = SkillstrainingTemplate::find($properties->foreign_id);
                if ($skillstraining) {
                    $moduleLink = "<a href='/#/tasks/skillstraining/{$skillstraining->id}' target='_blank'>{$skillstraining->title}</a>";
                    if ($properties->status === "completed") {
                        return "{$studentIdentifier} in {$location} completed {$moduleLink} Skills Training.";
                    } else {
                        return "{$studentIdentifier} in {$location} started {$moduleLink} Skills Training.";
                    }
                }
            }
        }

        if ($this->subject_type == "App\WorkexperienceResponse") {
            if (property_exists($properties, 'foreign_id')) {
                $workexperience = WorkexperienceTemplate::find($properties->foreign_id);
                if ($workexperience) {
                    $moduleLink = "<a href='/#/tasks/vwe/{$workexperience->id}' target='_blank'>{$workexperience->title}</a>";
                    if ($properties->status === "completed") {
                        return "{$studentIdentifier} in {$location} completed {$moduleLink} Virtual Work Experience.";
                    } else {
                        return "{$studentIdentifier} in {$location} started {$moduleLink} Virtual Work Experience.";
                    }
                }
            }
        }

        if ($this->subject_type == "App\StudentTemplateProgress") {
            if (property_exists($properties, 'foreign_id')) {
                $industryUnit = Industryunit::with('industries')->find($properties->foreign_id);
                $industryId = $industryUnit->industries->first()->id;
                if ($industryUnit) {
                    $moduleLink = "<a href='/#/exploreindustry/{$industryId}/template/{$industryUnit->id}' target='_blank'>{$industryUnit->title}</a>";
                    return "{$studentIdentifier} in {$location} looked at {$moduleLink}.";
                }
            }
        }

        return null;
    }

    /**
     * Get Game Plan activities for employer based on their company's industries
     */
    public static function getEmployerGamePlanActivities($employerId, $limit = 10)
    {
        $employer = \App\Employer::with('company.industries')->find($employerId);

        if (!$employer || !$employer->company) {
            return collect();
        }

        $companyIndustryIds = $employer->company->industries->pluck('id')->toArray();

        if (empty($companyIndustryIds)) {
            return collect();
        }

        // Get recent Game Plan industry additions that match company industries
        $industryActivities = \App\GameplanIndustry::with(['gameplan.user.profile', 'gameplan.user.state', 'industryCategory'])
            ->whereIn('industry_category_id', $companyIndustryIds)
            ->whereHas('gameplan.user.profile', function ($query) {
                $query->where('is_public', true); // Only public profiles
            })
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($gameplanIndustry) {
                $user = $gameplanIndustry->gameplan->user;
                $location = $user->state ? $user->state->name : 'Unknown location';
                $studentName = $user->profile->firstname;
                $industryName = $gameplanIndustry->industryCategory->name;

                return (object) [
                    'id' => 'gameplan_industry_' . $gameplanIndustry->id,
                    'type' => 'gameplan_industry',
                    'description' => "<a href='/students/{$user->id}' target='_blank'>{$studentName}</a> in {$location} added {$industryName} to their Game Plan.",
                    'created_at' => $gameplanIndustry->created_at,
                    'student_id' => $user->id,
                ];
            });

        // Get recent Game Plan job additions (all jobs since we don't have job-industry mapping)
        $jobActivities = \App\GameplanJob::with(['gameplan.user.profile', 'gameplan.user.state'])
            ->whereHas('gameplan.user.profile', function ($query) {
                $query->where('is_public', true); // Only public profiles
            })
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($gameplanJob) {
                $user = $gameplanJob->gameplan->user;
                $location = $user->state ? $user->state->name : 'Unknown location';
                $studentName = $user->profile->firstname;
                $jobTitle = $gameplanJob->job_title;

                return (object) [
                    'id' => 'gameplan_job_' . $gameplanJob->id,
                    'type' => 'gameplan_job',
                    'description' => "<a href='/students/{$user->id}' target='_blank'>{$studentName}</a> in {$location} added {$jobTitle} to their Game Plan.",
                    'created_at' => $gameplanJob->created_at,
                    'student_id' => $user->id,
                ];
            });

        // Combine and sort by created_at
        return $industryActivities->concat($jobActivities)
            ->sortByDesc('created_at')
            ->take($limit);
    }

    /**
     * Get combined timeline for employer including regular activities and Game Plan activities
     */
    public static function getEmployerTimeline($employerId, $limit = 20)
    {
        $employer = \App\Employer::with('company.industries')->find($employerId);

        if (!$employer || !$employer->company) {
            return collect();
        }

        // Get regular timeline activities (existing logged activities)
        $regularActivities = self::where('log_name', 'timeline')
            ->whereHas('causer.profile', function ($query) {
                $query->where('is_public', true); // Only public profiles
            })
            ->with(['causer.profile', 'causer.state'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($activity) {
                return (object) [
                    'id' => 'activity_' . $activity->id,
                    'type' => 'regular_activity',
                    'description' => $activity->customDescription(),
                    'created_at' => $activity->created_at,
                    'student_id' => $activity->causer_id,
                ];
            })
            ->filter(function ($activity) {
                return !empty($activity->description); // Filter out null descriptions
            });

        // Get Game Plan activities
        $gameplanActivities = self::getEmployerGamePlanActivities($employerId, $limit);

        // Combine and sort by created_at
        return $regularActivities->concat($gameplanActivities)
            ->sortByDesc('created_at')
            ->take($limit);
    }

    /**
     * Get student location (state name)
     */
    private function getStudentLocation($causer)
    {
        if ($causer->state_id) {
            $state = \App\State::find($causer->state_id);
            return $state ? $state->name : 'Unknown location';
        }
        return 'Unknown location';
    }

    /**
     * Get student identifier based on privacy settings
     */
    private function getStudentIdentifier($causer)
    {
        // Check if student profile is public
        if ($causer->profile && $causer->profile->is_public) {
            return $causer->profile->firstname;
        }
        return 'A student';
    }

    /**
     * Get student identifier with hyperlink based on privacy settings
     */
    private function getStudentIdentifierWithLink($causer)
    {
        // Check if student profile is public
        if ($causer->profile && $causer->profile->is_public) {
            $studentName = $causer->profile->firstname;
            return "<a href='/students/{$causer->id}' target='_blank'>{$studentName}</a>";
        }
        return 'A student';
    }
}
