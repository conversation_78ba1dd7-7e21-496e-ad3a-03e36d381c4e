<template>


    <div class=" card mt-20 mt-md-10 mt-xl-5">
        <!-- <div class=" card-header align-items-center border-0">

        </div> -->

        <div class="card-body pb-0">
            <div class="d-flex justify-content-between align-items-center mb-10">
                <h3>Company Page</h3>

                <button v-if="activeTab === 'LinkedCourses'" type="button" class="btn btn-secondary d-flex align-items-center mt-5 me-10">
                    Update Details
                </button>
                <button type="button" class="btn btn-secondary">
                    <i class="bi bi-eye-fill fs-3"></i>
                    View Company Page
                </button>
            </div>
           <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold">
    <li class="nav-item">
        <router-link
            :to="{ name: 'company', query: { tab: 'CompanyProfile' } }"
            class="nav-link text-active-primary"
            :class="{ active: activeTab === 'CompanyProfile' }"
        >
            Company Profile
        </router-link>
    </li>
    <li class="nav-item">
        <router-link
            :to="{ name: 'company', query: { tab: 'CompanyPages' } }"
            class="nav-link text-active-primary"
            :class="{ active: activeTab === 'CompanyPages' }"
        >
            Company Pages
        </router-link>
    </li>
    <li class="nav-item">
        <router-link
            :to="{ name: 'company', query: { tab: 'JobsandOpportunities' } }"
            class="nav-link text-active-primary"
            :class="{ active: activeTab === 'JobsandOpportunities' }"
        >
            Jobs and Opportunities
        </router-link>
    </li>
    <li class="nav-item">
        <router-link
            :to="{ name: 'company', query: { tab: 'LinkedCourses' } }"
            class="nav-link text-active-primary"
            :class="{ active: activeTab === 'LinkedCourses' }"
        >
            Linked Courses
        </router-link>
    </li>
    <li class="nav-item">
        <router-link
            :to="{ name: 'company', query: { tab: 'LinkedContent' } }"
            class="nav-link text-active-primary"
            :class="{ active: activeTab === 'LinkedContent' }"
        >
            Linked Content
        </router-link>
    </li>
</ul>
        </div>
    </div>




    <div>
        <CompanyProfile v-if="activeTab === 'CompanyProfile'" />

        <CompanyPages v-if="activeTab === 'CompanyPages'" />

        <JobsAndOpportunities v-if="activeTab === 'JobsandOpportunities'" />

        <LinkedCourses v-if="activeTab === 'LinkedCourses'" />

        <LinkedContent v-if="activeTab === 'LinkedContent'" />

        <div v-if="activeTab === 'Content'">
            Linked Content placeholder
        </div>
    </div>


    <!-- Nav Card -->
</template>


<script lang="ts">
    import { defineComponent, ref,onMounted } from "vue";
    import { useRoute } from "vue-router";
    import CompanyProfile from "@/components/employer/company/CompanyProfile.vue";
    import CompanyPages from "@/components/employer/company/CompanyPages.vue";
    import JobsAndOpportunities from "@/components/employer/company/JobsandOpportunities.vue";
    import LinkedCourses from "@/components/employer/company/LinkedCourses.vue";
    import LinkedContent from "@/components/employer/company/LinkedContent.vue";

    export default defineComponent({
        name: "CompanyPagesTabs",
        components: { CompanyProfile, CompanyPages, JobsAndOpportunities, LinkedCourses, LinkedContent },
        setup() {
            const route = useRoute();
        const activeTab = ref("CompanyProfile");

        // Valid tabs to prevent invalid tab values
        const validTabs = [
            "CompanyProfile",
            "CompanyPages",
            "JobsandOpportunities",
            "LinkedCourses",
            "LinkedContent",
        ];

        onMounted(() => {
            const tab = route.query.tab;
            if (typeof tab === "string" && validTabs.includes(tab)) {
                activeTab.value = tab;
            }
        });

        return { activeTab };
    
        },
    });
</script>


<style scoped>
    .nav-link {
        cursor: pointer;
    }
    .nav-link:not(.active) {
        font-weight: normal;
    }

</style>