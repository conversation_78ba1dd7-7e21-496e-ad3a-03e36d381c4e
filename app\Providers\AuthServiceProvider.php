<?php

namespace App\Providers;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use App\Providers\CacheUserProvider;
class AuthServiceProvider extends ServiceProvider {
	/**
	 * The policy mappings for the application.
	 *
	 * @var array
	 */
	protected $policies = [
		'App\School' => 'App\Policies\SchoolPolicy',
		'App\Thread' => 'App\Policies\ThreadPolicy',
		'App\Reply' => 'App\Policies\ReplyPolicy',
		'App\User' => 'App\Policies\UserPolicy',
		'App\JobPost' => 'App\Policies\JobPostPolicy',
	];

	/**
	 * Register any authentication / authorization services.
	 *
	 * @return void
	 */
	public function boot() {
		$this->registerPolicies();
// Caching user
        Auth::provider('cache-user', function() {
            return resolve(CacheUserProvider::class);
        });
//        Gate::before(function ($user) {
		//            if ($user->name === '<PERSON>') return true;
		//        });
	}
}
