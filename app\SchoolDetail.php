<?php

namespace App;

use DB;
use Auth;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use App\Enums\InstituteType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Support\Facades\Cache;

class SchoolDetail extends Model
{
    use Sluggable;
    // protected $table = "school_details";

    /**
     * Don't auto-apply mass assignment protection.
     *
     * @var array
     */
    protected $guarded = [];
    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    protected $dates = [
        'confirmed_at',
        // 'subscription_ending_on'
    ];

    protected $casts = [
        'institute_type' => InstituteType::class,
        'social_links' => 'array',
    ];

    protected $appends = ['logo_full_path','banner_image_full_Path'];

    protected static function booted()
    {

        static::updated(function ($detail) {
            Cache::tags(['school_' . $detail->school_id])->flush();
            Cache::tags(['userhasProAccess', 'school.' . $detail->school_id])->flush();
            Cache::forget('schoolPlans' . $detail->school_id);
            Cache::forget('standards' . $detail->school_id);
            foreach (Standard::all() as $key => $year) {
                Cache::forget('wewmenuY' . $year->id . 'S' . $detail->school_id);
            }
            Cache::tags(['schoolmenuAccess'])->forget("schoolMenuAccess-" . $detail->school_id);
        });

         static::deleted(function ($detail) {
            Cache::tags(['school_' . $detail->school_id])->flush();
            Cache::tags(['userhasProAccess', 'school.' . $detail->school_id])->flush();
            Cache::forget('schoolPlans' . $detail->school_id);
            Cache::forget('standards' . $detail->school_id);
            foreach (Standard::all() as $key => $year) {
                Cache::forget('wewmenuY' . $year->id . 'S' . $detail->school_id);
            }
            Cache::tags(['schoolmenuAccess'])->forget("schoolMenuAccess-" . $detail->school_id);
        });

        static::deleting(function ($detail) {
            if ($detail->logo && Storage::exists($detail->logo)) {
                Storage::delete($detail->logo);
            }
        });
    }

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'name',
                'onUpdate' => true,
            ],
        ];
    }

    public function getConfirmedAtAttribute($value)
    {
        if ($value) {
            return Carbon::parse($value)->toFormattedDateString();
        }
    }

    public function getSubscriptionEndingOnAttribute($value)
    {
        if ($value) {
            return Carbon::parse($value)->toDateString();
        }

        if ($this->subscription_start_date) {
            return Carbon::parse($this->subscription_start_date)->addYears(1)->toDateString();
        }
        if ($this->order_confirmed) {
            return Carbon::createFromFormat('M d, Y', $this->confirmed_at)->addYears(1)->toDateString();
        }

        return Carbon::now()->addYears(1)->toDateString();
    }

    public function school()
    {
        return $this->belongsTo(School::class);
    }

    public function organisation()
    {
        return $this->belongsTo(Organisation::class, 'school_id');
    }

    public function getLogoFullPathAttribute()
    {
        return $this->logo ? Storage::url($this->logo) : null;
    }

    public function getBannerImageFullPathAttribute()
    {
        if ($this->banner) {
            return Storage::url($this->banner);
        }
    }

    public function getTotalStudentsAttribute()
    {
        $parent = $this->school ?? $this->organisation;
        return $parent->students()->whereHas('profile', function ($query) {
            $query->where('accountcreated', true)->where('standard_id', '<>', 7)->whereRemoved(false);
        })->count();
    }

    public function getTotalGraduatedStudentsAttribute()
    {
        $parent = $this->school ?? $this->organisation;
        return $parent->students()->whereHas('profile', function ($query) {
            $query->where('accountcreated', true)->where('standard_id', 7);
        })->count();
    }

    public function getTotalInactiveStudentsAttribute()
    {
        $parent = $this->school ?? $this->organisation;
        return $parent->students()->whereHas('profile', function ($query) {
            $query->where('accountcreated', true)->where('standard_id', '<>', 7)->whereRemoved(true);
        })->count();
    }

    public function getTotalTeachersAttribute()
    {
        $parent = $this->school ?? $this->organisation;
        if (Auth::user() && Auth::user()->isStaff()) {
            return $parent->staff()->count();
        } else {
            return $parent->teachers()->count();
        }
    }

    public function getLastActiveStudentsAttribute()
    {
        $parent = $this->school ?? $this->organisation;
        return UserSessionStat::select(
            'users.id',
            'users.name',
            'user_session_stats.last_seen as last_active'
        )->join('users', 'user_session_stats.user_id', '=', 'users.id')->where(['users.school_id' => $parent->id, 'users.role_id' => 3])->orderBy('last_active', 'desc')->limit(20)->get();

        // return UserSession::select(
        //     'users.id',
        //     'users.name',
        //     DB::raw('MAX(loggedout_at) as last_active')
        // )->join('users', 'user_sessions.user_id', '=', 'users.id')->where(['users.school_id' => $parent->id, 'users.role_id' => 3])->orderBy('last_active', 'desc')
        //     ->groupBy("user_id")->limit(20)->get();
        // return $parent->students()->select(
        //     'users.id',
        //     'users.name',
        //     DB::raw('MAX(loggedout_at) as last_active')
        // )->orderBy('last_active', 'desc')
        // ->groupBy("user_id")
        // ->limit(20)->get();
        //  return $parent->students()->select(
        //     'users.id',
        //     'users.name',
        //     DB::raw('(select loggedout_at from user_sessions where user_id  =   users.id  and duration is not null order by loggedout_at desc limit 1) as last_active')
        // )->orderBy('last_active', 'desc')
        // ->whereNotNull(DB::raw('(select loggedout_at from user_sessions where user_id  =   users.id  and duration is not null order by loggedout_at desc limit 1)'))->limit(20)->get();
    }

    // public function getLastActiveStudentsAttribute()
    // {
    //     $parent = $this->school ?? $this->organisation;

    //     return $parent->students()
    //     ->select('users.id', 'users.name', 'logins.lastseen_at as last_active',)
    //     ->join('logins', 'logins.user_id', '=', 'users.id')
    //     ->orderBy('last_active', 'desc')
    //     ->limit(20)->get();
    // }



    public function getLast30DaysLoginAttribute()
    {
        $parent = $this->school ?? $this->organisation;
        $sec = $parent->students()
            ->join('user_sessions', function ($join) {
                $join->on('users.id', '=', 'user_sessions.user_id')
                    ->whereDate('user_sessions.loggedin_at', '>', Carbon::now()->subDays(30));
            })
            ->groupBy('users.id')
            ->get(['users.id', DB::raw('sum(user_sessions.duration) as seconds')])
            ->sum('seconds');
        if ($sec) {
            $logins = CarbonInterval::seconds($sec)->cascade()->forHumans(['short' => true]);
            $parts = explode(' ', $logins);
            $data = (count($parts) > 1) ? $parts[0] . ' ' . $parts[1] : $parts[0];
        } else {
            $data = '-';
        }

        return $data;
        // return $sec ? CarbonInterval::seconds($sec)->cascade()->forHumans(['short' => false]) : '-';
    }

    public function getMinutesSpentAttribute()
    {
        $parent = $this->school ?? $this->organisation;
        $sec = $parent->students()
            ->join('user_sessions', 'user_sessions.user_id', '=', 'users.id')
            ->groupBy('users.id')
            ->get(['users.id', DB::raw('sum(user_sessions.duration) as seconds')])
            ->sum('seconds');

        return round($sec / 60);
    }

    // public function getMinutesSpentAttribute()
    // {
    //     $parent = $this->school ?? $this->organisation;
    //     $sec = $parent->students()
    //     ->select('users.id', 'logins.total_duration as seconds',)
    //     ->join('logins', 'logins.user_id', '=', 'users.id')
    //     ->sum('seconds');

    //     return round($sec / 60);
    // }

    public function getTotalProfilingAttribute()
    {
        $parent = $this->school ?? $this->organisation;
        return $parent->students()->has('profilerResult')->count();
    }

    public function getStudentsLoginAttribute()
    {
        $schoolid = $this->school_id;
        return UserSessionStat::whereHas("user", function ($query) use ($schoolid) {
            $query->where('school_id', $schoolid);
        })->sum('login_count');
        // return UserSession::whereHas("user", function ($query) use ($schoolid) {
        //     $query->where('school_id', $schoolid);
        // })->count();
    }

    // public function getStudentsLoginDurationAttribute()
    // {
    //     $schoolid = $this->school_id;
    //     $sec = UserSessionStat::whereHas("user", function ($query) use ($schoolid) {
    //         $query->where('school_id', $schoolid);
    //     })->sum('total_duration');

    //     if ($sec) {
    //         $logins = CarbonInterval::seconds($sec)->cascade()->forHumans(['short' => true]);
    //         $parts = explode(' ', $logins);
    //         $data = (count($parts) > 1) ? $parts[0] . ' ' . $parts[1] : $parts[0];
    //     } else {
    //         $data = '-';
    //     }
    //     return $data;
    // }

    public function getStudentsLoginDurationAttribute()
    {
        $schoolid = $this->school_id;

        $sec = UserSessionStat::whereHas('user', function ($query) use ($schoolid) {
            $query->where('school_id', $schoolid);
        })->sum('total_duration');

        if ($sec) {
            $hours = $sec / 3600;
            $formatted = ($hours >= 1000)
                ? number_format($hours / 1000, 1) . 'k hrs'
                : number_format($hours, 1) . ' hrs';
        } else {
            $formatted = '-';
        }

        return $formatted;
    }

    // public function getStudentsLoginAttribute()
    // {
    //     $parent = $this->school ?? $this->organisation;
    //     return $parent->students()
    //     ->select('users.id', 'logins.total_count as count',)
    //     ->join('logins', 'logins.user_id', '=', 'users.id')
    //     ->sum('count');
    // }


    public function getAvgTimeAttribute()
    {
        $parent = $this->school ?? $this->organisation;
        $avg = $parent->students()->get('id')->avg('avg_session_duration');
        return $avg ? CarbonInterval::seconds(round($avg))->cascade()->forHumans(['short' => true]) : '-';
    }

    public function hasSecondarySchoolAccess()
    {
        if ($this->secondary_section == 1) {
            return true;
        }

        return false;
    }

    public function hasPrimarySchoolAccess()
    {
        if ($this->primary_section == 1) {
            return true;
        }

        return false;
    }

    public function hasFullSchoolAccess()
    {
        if ($this->secondary_section == 1 && $this->primary_section == 1) {
            return true;
        }

        return false;
    }
}
