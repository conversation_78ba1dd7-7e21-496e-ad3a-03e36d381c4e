<link class="main-stylesheet" href="{{ asset('css/custom_mt.css') }}" rel="stylesheet" type="text/css" />
<script src="https://unpkg.com/@popperjs/core@2" type="text/javascript"></script>
<script src="{{ asset('js/kt_met/event-handler.js') }}" type="text/javascript"></script>
<script src="{{ asset('js/kt_met/util.js') }}" type="text/javascript"></script>
<script src="{{ asset('js/kt_met/menu.js') }}" type="text/javascript"></script>
<div id="kt_aside" class="app-aside flex-column" data-kt-drawer="true" data-kt-drawer-name="aside" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="140px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <div class="aside-logo py-8 text-center" id="kt_app_sidebar_logo">
        <a href="#/" class="router-link-active">
            <img alt="Logo" src="/media/logos/default-small.svg" class="h-36px app-sidebar-logo-default">
        </a>
    </div>
    <div class="aside-menu flex-column-fluid">
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper hover-scroll-overlay-y my-5" data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer" data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true" style="height: 359px;">
            <div id="#kt_app_sidebar_menu" class="menu menu-column menu-title-gray-700 menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary menu-arrow-gray-500 fw-semibold" data-kt-menu="true">
                <div class="menu-item py-2">
                    <a aria-current="page" href="/#/dashboard" class="active router-link-exact-active menu-link menu-center flex-column">
                        <span class="menu-icon m-0">
                            <span class="svg-icon svg-icon-2">
                                <svg viewBox="0 0 22.172 20.531" width="19.5" height="19.5" xmlns="http://www.w3.org/2000/svg">
                                    <path xmlns="http://www.w3.org/2000/svg" d="M13.367,2.594l-.581.556L2.281,13.655l1.162,1.162,1.035-1.035v9.344h7.273V15.044h3.232v8.081h7.273V13.781l1.035,1.035,1.162-1.162L13.948,3.149Zm0,2.3,7.273,7.273v9.344H16.6V13.427H10.135v8.081H6.094V12.165Z" transform="translate(-2.281 -2.594)" fill="#707070">
                                    </path>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Home</span>
                    </a>
                </div>
                <div class="menu-item py-2">
                    <a class="menu-link menu-center flex-column" active-class="active" href="/profiles/edit">
                        <span class="menu-icon m-0">
                            <span class="svg-icon svg-icon-2">
                                <svg viewBox="0 0 18.176 19.994" width="25" height="25" xmlns="http://www.w3.org/2000/svg">
                                    <path xmlns="http://www.w3.org/2000/svg" d="M15.088,5a6.364,6.364,0,0,0-3.55,11.644A9.115,9.115,0,0,0,6,24.994H7.818a7.27,7.27,0,1,1,14.541,0h1.818a9.115,9.115,0,0,0-5.538-8.35A6.364,6.364,0,0,0,15.088,5Zm0,1.818a4.544,4.544,0,1,1-4.544,4.544A4.53,4.53,0,0,1,15.088,6.818Z" transform="translate(-6 -5)" fill="#707070">
                                    </path>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Profile</span>
                    </a>
                </div>
                <div class="menu-item py-2" data-kt-menu-trigger="click">
                    <span class="menu-link menu-center flex-column">
                        <span class="menu-icon m-0">
                            <span class="svg-icon svg-icon-2">
                                <svg viewBox="0 0 25.491 14.626" width="25" height="25" xmlns="http://www.w3.org/2000/svg">
                                    <path xmlns="http://www.w3.org/2000/svg" d="M7.8,7a5,5,0,0,0-5.1,4.875,4.817,4.817,0,0,0,2.1,3.936A6.485,6.485,0,0,0,1,21.626H2.7A4.979,4.979,0,0,1,7.8,16.75a4.979,4.979,0,0,1,5.1,4.875h1.7a4.979,4.979,0,0,1,5.1-4.875,4.979,4.979,0,0,1,5.1,4.875h1.7a6.485,6.485,0,0,0-3.8-5.815,4.817,4.817,0,0,0,2.1-3.936,5.1,5.1,0,0,0-10.2,0,4.817,4.817,0,0,0,2.1,3.936,6.733,6.733,0,0,0-2.947,2.666A6.733,6.733,0,0,0,10.8,15.811a4.817,4.817,0,0,0,2.1-3.936A5,5,0,0,0,7.8,7Zm0,1.625a3.313,3.313,0,0,1,3.4,3.25,3.313,3.313,0,0,1-3.4,3.25,3.313,3.313,0,0,1-3.4-3.25A3.313,3.313,0,0,1,7.8,8.625Zm11.9,0a3.313,3.313,0,0,1,3.4,3.25,3.4,3.4,0,0,1-6.8,0A3.313,3.313,0,0,1,19.693,8.625Z" transform="translate(-1 -7)" fill="#707070">
                                    </path>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Accounts</span>
                    </span>
                    <div class="menu-sub menu-sub-dropdown menu-sub-indention rounded-0" style="">
                        <div class="menu-item">
                            @if(Auth::user()->childInvitees()->exists())
                                @foreach (Auth::user()->childInvitees as $invitee)
                                    @if($invitee->processed)
                                        <a href="/profiles/edit/{{ $invitee->child_id }}" class="active menu-link">
                                            <span class="menu-title">{{ $invitee->child->name }}</span>
                                        </a>
                                    @else
                                        @if ($invitee->child->profile->accountcreated)
                                            <span class="menu-link" data-toggle="tooltip" title="Pending account connection. {{ $invitee->child->name }} has an account but has not connected it to yours yet. You can reinvite them to do this from your profile.">
                                                <span class="menu-title">{{ $invitee->child->name ?? $invitee->child->email}}</span>
                                            </span>
                                        @else
                                            <span class="menu-link" data-toggle="tooltip" title="Pending account creation. {{ $invitee->child->name }} has received an invitation to create their account and connect it to yours, but has not set this up yet. You can reinvite them to do this from your profile.">
                                                <span class="menu-title">{{ ($invitee->child->name != null) ? $invitee->child->name:$invitee->child->email}}</span>
                                            </span>
                                        @endif
                                    @endif
                                @endforeach
                            @endif

                            <a href="javascript:void(0);" class="active menu-link add-child-btn">
                                <span class="menu-title">Add Child +</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="menu-item py-2" data-kt-menu-trigger="click">
                    <span class="menu-link menu-center flex-column">
                        <span class="menu-icon m-0">
                            <span class="svg-icon svg-icon-2">
                                <svg viewBox="0 0 15.514 20.168" width="25" height="25" xmlns="http://www.w3.org/2000/svg">
                                    <path xmlns="http://www.w3.org/2000/svg" d="M6,3V23.168H21.514V8.115L21.3,7.872,16.641,3.218,16.4,3ZM7.551,4.551h7.757V9.205h4.654V21.616H7.551ZM16.86,5.666l1.988,1.988H16.86Zm-5.43,5.26v7.417l1.164-.727,3.878-2.327,1.115-.654-1.115-.654-3.878-2.327Zm1.551,2.739,1.624.97-1.624.97Z" transform="translate(-6 -3)" fill="#707070"></path>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Explore</span>
                    </span>
                    <div class="menu-sub menu-sub-dropdown menu-sub-indention rounded-0 " style="">
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/exploreindustries">
                                <span class="menu-title">Industries</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/e-magazines/editions">
                                <span class="menu-title">e-Magazine</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="menu-item py-2" data-kt-menu-trigger="click">
                    <span class="menu-link menu-center flex-column">
                        <span class="menu-icon m-0">
                            <span class="svg-icon svg-icon-2">
                                <svg viewBox="0 0 23.78 16.987" width="25" height="25" xmlns="http://www.w3.org/2000/svg">
                                    <path xmlns="http://www.w3.org/2000/svg" d="M4.548,6V17.519L2.61,19.483A2.082,2.082,0,0,0,2,20.943a2.053,2.053,0,0,0,2.044,2.044H23.737a2.053,2.053,0,0,0,2.044-2.044,2.082,2.082,0,0,0-.61-1.46l-1.937-1.964V6Zm1.7,1.7H21.534v9.343H6.247ZM5.769,18.74H22.012l1.964,1.938a.4.4,0,0,1,.106.265.327.327,0,0,1-.345.345H4.044a.327.327,0,0,1-.345-.345.4.4,0,0,1,.106-.265Z" transform="translate(-2 -6)" fill="#707070">
                                    </path>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Tasks</span>
                    </span>
                    <div class="menu-sub menu-sub-dropdown menu-sub-indention rounded-0" style="">
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/tasks">
                                <span class="menu-title">Lessons</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/exploreworkexperience">
                                <span class="menu-title">Virtual Work Experience</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/wew/skillstraining">
                                <span class="menu-title">Skills Training</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="menu-item py-2" data-kt-menu-trigger="click">
                    <span class="menu-link menu-center flex-column">
                        <span class="menu-icon m-0">
                            <span class="svg-icon svg-icon-2">
                                <svg viewBox="0 0 21.711 17.059" width="25" height="25" xmlns="http://www.w3.org/2000/svg">
                                    <path xmlns="http://www.w3.org/2000/svg" d="M10.529,5A1.562,1.562,0,0,0,8.978,6.551V8.1H2V22.059H23.711V8.1H16.732V6.551A1.562,1.562,0,0,0,15.181,5Zm0,1.551h4.652V8.1H10.529Zm-6.978,3.1H22.16v3.877h-3.1v-.775h-3.1v.775h-6.2v-.775h-3.1v.775h-3.1Zm0,5.428h3.1v.775h3.1V15.08h6.2v.775h3.1V15.08h3.1v5.428H3.551Z" transform="translate(-2 -5)" fill="#707070">
                                    </path>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Tools</span>
                    </span>
                    <div class="menu-sub menu-sub-dropdown menu-sub-indention rounded-0" style="">
                        <div class="menu-item">
                            {{-- <a class="menu-link" active-class="active" target="_blank" href="/tools/jobfinder">
                                <span class="menu-title">Job Finder</span>
                            </a> --}}
                             <a class="menu-link" active-class="active" target="_blank" href="/#/tools/jobfinder">
                                <span class="menu-title">Job Finder</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/scholarships">
                                <span class="menu-title">Scholarship Finder</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/cvs">
                                <span class="menu-title">Resume Builder</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/coursefinder">
                                <span class="menu-title">Course Finder</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/eportfolio">
                                <span class="menu-title">ePortfolio</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/subjects-selection">
                                <span class="menu-title">Subject Selections</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="menu-item py-2" data-kt-menu-trigger="click">
                    <span class="menu-link menu-center flex-column">
                        <span class="menu-icon m-0">
                            <span class="svg-icon svg-icon-2">
                                <svg preserveAspectRatio="xMidYMid meet" width="25" height="25" viewBox="0 0 512 512" version="1.0" xmlns="http://www.w3.org/2000/svg">
                                    <g xmlns="http://www.w3.org/2000/svg" transform="translate(0,512) scale(0.1,-0.1)" fill="#707070" stroke="none">
                                        <path d="M2415 5010 c-423 -28 -831 -177 -1170 -430 -475 -354 -784 -872 -869 -1461 -9 -59 -16 -132 -16 -163 l0 -56 -65 -40 c-116 -74 -196 -182 -230 -311 -22 -81 -21 -771 0 -854 23 -87 62 -152 136 -227 143 -145 359 -196 514 -123 62 30 138 107 168 173 22 47 22 53 22 607 l0 560 -28 56 c-44 91 -140 167 -231 184 l-38 8 7 78 c16 191 84 433 175 626 192 407 510 730 915 928 731 358 1597 227 2196 -333 335 -314 564 -777 605 -1226 l7 -73 -39 -7 c-91 -18 -187 -94 -231 -185 l-28 -56 0 -560 c0 -553 0 -561 22 -607 45 -97 175 -198 256 -198 l27 0 0 -137 c0 -156 -15 -233 -59 -321 -59 -116 -163 -203 -289 -242 -56 -17 -93 -20 -281 -20 l-217 0 -12 32 c-21 58 -97 134 -170 170 l-67 33 -280 0 -280 0 -66 -32 c-79 -39 -140 -102 -177 -181 -23 -49 -27 -70 -27 -152 1 -84 4 -102 29 -154 35 -72 97 -136 164 -170 77 -39 169 -49 407 -44 187 4 216 7 267 27 71 28 161 107 193 172 l24 47 253 4 c227 4 260 7 318 26 199 67 355 201 440 377 62 129 71 176 77 403 l6 203 41 21 c102 52 217 205 245 328 17 73 14 773 -4 839 -34 129 -114 237 -230 311 l-65 40 0 55 c0 88 -29 270 -66 415 -95 370 -288 711 -557 983 -330 334 -730 546 -1182 627 -91 17 -384 43 -420 38 -5 0 -59 -4 -120 -8z m-1805 -2337 c16 -9 33 -27 39 -40 16 -35 15 -983 -1 -1018 -13 -28 -55 -55 -86 -55 -87 0 -197 76 -238 165 -24 49 -24 54 -24 400 0 347 0 350 24 400 21 45 56 84 109 125 19 14 99 38 130 39 11 1 32 -6 47 -16z m4046 -8 c50 -21 115 -86 140 -140 24 -49 24 -54 24 -400 0 -346 0 -351 -24 -400 -41 -89 -151 -165 -238 -165 -31 0 -73 27 -86 55 -18 40 -16 991 2 1024 28 51 98 61 182 26z m-1268 -2095 c18 -11 40 -35 49 -55 22 -47 6 -105 -40 -137 -30 -22 -42 -23 -232 -26 -231 -4 -266 3 -303 58 -40 58 -13 143 53 170 11 5 115 9 230 9 193 1 213 -1 243 -19z"></path>
                                        <path d="M1656 3834 c-132 -32 -260 -131 -323 -250 -62 -116 -63 -130 -63 -760 0 -338 4 -593 10 -625 16 -84 76 -196 140 -260 84 -85 228 -149 332 -149 l38 0 0 -162 c0 -142 3 -169 21 -208 40 -89 111 -138 211 -147 106 -10 123 2 401 274 l249 243 367 0 c293 0 378 3 424 15 186 48 328 192 372 376 22 95 22 1183 0 1278 -43 183 -193 333 -376 376 -92 21 -1713 21 -1803 -1z m1769 -251 c65 -22 136 -93 158 -158 15 -43 17 -112 17 -605 0 -612 0 -609 -61 -683 -17 -20 -52 -49 -77 -65 l-47 -27 -425 -6 c-234 -3 -427 -6 -430 -7 -2 -1 -120 -118 -262 -259 l-258 -257 0 207 c0 302 -4 308 -210 317 -114 5 -131 9 -172 33 -26 15 -60 44 -77 64 -61 74 -61 71 -61 683 0 635 -2 619 80 701 82 82 30 77 956 78 741 1 825 -1 869 -16z"></path>
                                        <path d="M1888 2960 c-63 -34 -83 -73 -83 -160 0 -63 4 -80 23 -108 78 -109 257 -91 313 32 36 79 13 169 -56 222 -50 38 -141 44 -197 14z"></path>
                                        <path d="M2485 2966 c-16 -8 -42 -26 -57 -42 -108 -116 -26 -304 131 -304 159 0 241 188 133 304 -51 54 -140 72 -207 42z"></path>
                                        <path d="M3065 2966 c-58 -26 -105 -101 -105 -168 1 -49 33 -112 76 -144 33 -25 46 -29 104 -29 121 0 186 69 178 189 -4 74 -31 117 -88 146 -45 23 -120 26 -165 6z"></path>
                                    </g>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Support</span>
                    </span>
                    <div class="menu-sub menu-sub-dropdown menu-sub-indention rounded-0" style="">
                        {{-- <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/noticeboard">
                                <span class="menu-title">Noticeboard</span>
                            </a>
                        </div> --}}
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" target="_blank" href="https://help.thecareersdepartment.com/en/">
                                <span class="menu-title">Help Centre</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    jQuery(document).ready(function() {
        KTMenu.init();
    });
</script>
<style>
    .footer-icon:hover svg path, .aside-menu .menu > .menu-item:not(.here) > .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-icon .svg-icon svg path, .menu-item .menu-link.active .menu-icon .svg-icon svg path{
        fill: #fff;
    }
     .footer-icon:hover svg path, .aside-menu .menu > .menu-item > .menu-link:hover .menu-title, .aside-menu .menu > .menu-item > .menu-link.active .menu-title {
        color: #fff !important;
    }
</style>

@push('scripts')
    <script>
        $(function () {
            function redirectToAddChild () {
                const url = "{{ url('/#/dashboard') }}";
                localStorage.setItem('showAddChildModal', 1);
                window.location.href = url;
            }

            $('.add-child-btn').on('click', redirectToAddChild);
        });
    </script>
@endpush
